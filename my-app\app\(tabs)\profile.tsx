import { useAuth } from '../../contexts/AuthContext';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
    Alert,
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Mock components and hooks for now
const LanguageSwitcher = ({ visible, onClose }: { visible: boolean; onClose: () => void }) => null;

const useTheme = () => ({
  colors: {
    primary: '#1ca63a',
    secondary: '#df5921',
    background: '#F8F9FA',
    surface: '#FFFFFF',
    text: '#1A1A1A',
    textSecondary: '#666666',
    border: '#E1E5E9',
    success: '#34C759',
    warning: '#FF9500',
    error: '#FF3B30',
    info: '#007AFF',
    card: '#FFFFFF',
    shadow: '#000000',
  },
  isDark: false,
  theme: 'light' as const,
  setTheme: (theme: 'light' | 'dark' | 'system') => {},
});

const useTranslation = () => ({
  t: (key: string) => key,
  i18n: { language: 'en' },
});

export default function ProfileScreen() {
  const { user, logout } = useAuth();
  const { colors, theme, setTheme } = useTheme();
  const { t, i18n } = useTranslation();
  const [showLanguageSwitcher, setShowLanguageSwitcher] = React.useState(false);

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: () => {
            logout();
            router.replace('/login');
          },
        },
      ]
    );
  };

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
  };

  // Update the first menu item in the menuItems array
const menuItems = [
  {
    icon: 'person-outline',
    title: 'Edit Profile',
    subtitle: 'Update your personal information',
    onPress: () => {
      if (user?.status === 'approved') {
        Alert.alert(
          'Profile Locked',
          'Your profile has been approved and cannot be edited. Please contact support for any changes.',
          [{ text: 'OK' }]
        );
      } else {
        router.push('/edit-profile');
      }
    },
  },
  // ... other menu items

    {
      icon: 'moon-outline',
      title: 'Theme',
      subtitle: `Current: ${theme.charAt(0).toUpperCase() + theme.slice(1)}`,
      onPress: () => {
        Alert.alert(
          'Select Theme',
          'Choose your preferred theme',
          [
            { text: 'Light', onPress: () => handleThemeChange('light') },
            { text: 'Dark', onPress: () => handleThemeChange('dark') },
            { text: 'System', onPress: () => handleThemeChange('system') },
            { text: 'Cancel', style: 'cancel' },
          ]
        );
      },
    },
    // {
    //   icon: 'settings-outline',
    //   title: 'Settings',
    //   subtitle: 'App preferences and configurations',
    //   onPress: () => Alert.alert('About', 'Professional Login App v1.0.0\nBuilt with React Native & Expo'),
    // },
    {
      icon: 'help-circle-outline',
      title: 'Help & Support',
      subtitle: 'Get help and contact support',
      
                    onPress:() => router.push('/contact')

    },
    {
      icon: 'information-circle-outline',
      title: 'About',
      subtitle: 'App version and information',
      onPress: () => Alert.alert('Coming Soon', 'This feature will be available soon!'),
    },
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {/* Header */}
        <View style={[styles.header, { backgroundColor: colors.surface }]}>
          <View style={[styles.avatarContainer, { backgroundColor: colors.primary + '20' }]}>
            <View style={[styles.avatarBorder, { borderColor: colors.primary }]}>
              {user?.profilePhoto ? (
                <Image
                  source={{ uri: user.profilePhoto }}
                  style={styles.avatarImage}
                />
              ) : (
                <Ionicons name="person" size={50} color={colors.primary} />
              )}
            </View>
          </View>
          <Text style={[styles.name, { color: colors.text }]}>{user?.fullName}</Text>
          <Text style={[styles.phoneNumber, { color: colors.textSecondary }]}>{user?.phoneNumber}</Text>
          <View style={[styles.roleContainer, { backgroundColor: colors.primary }]}>
            <Text style={styles.roleText}>{user?.role?.toUpperCase()}</Text>
          </View>

          {/* Points Display */}
          <View style={styles.pointsDisplay}>
            <Text style={[styles.pointsLabel, { color: colors.textSecondary }]}>
              Total Points
            </Text>
            <Text style={[styles.pointsValue, { color: colors.primary }]}>
              {user?.points || 0}
            </Text>
          </View>
        </View>

        {/* Menu Items */}
        <View style={[styles.menuContainer, { backgroundColor: colors.surface }]}>
          {menuItems.map((item, index) => {
            // Cycle through our color palette for variety
            const iconColors = [colors.primary, colors.secondary, colors.warning, colors.info];
            const iconColor = iconColors[index % iconColors.length];

            return (
              <TouchableOpacity
                key={index}
                style={[styles.menuItem, { borderBottomColor: colors.border }]}
                onPress={item.onPress}
              >
                <View style={styles.menuItemLeft}>
                  <View style={[styles.menuIconContainer, { backgroundColor: iconColor + '20' }]}>
                    <Ionicons name={item.icon as any} size={24} color={iconColor} />
                  </View>
                  <View style={styles.menuTextContainer}>
                    <Text style={[styles.menuTitle, { color: colors.text }]}>{item.title}</Text>
                    <Text style={[styles.menuSubtitle, { color: colors.textSecondary }]}>{item.subtitle}</Text>
                  </View>
                </View>
                <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Logout Button */}
        <TouchableOpacity
          style={[styles.logoutButton, { backgroundColor: colors.error + '20', borderColor: colors.error }]}
          onPress={handleLogout}
        >
          <Ionicons name="log-out-outline" size={24} color={colors.error} />
          <Text style={[styles.logoutText, { color: colors.error }]}>Logout</Text>
        </TouchableOpacity>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: colors.textSecondary }]}>
            Logged in as {user?.role} user
          </Text>
          <Text style={[styles.versionText, { color: colors.textSecondary }]}>Version 1.0.0</Text>
        </View>
      </ScrollView>

      {/* Language Switcher Modal */}
      <LanguageSwitcher
        visible={showLanguageSwitcher}
        onClose={() => setShowLanguageSwitcher(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 32,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  avatarContainer: {
    marginBottom: 20,
    alignItems: 'center',
    borderRadius: 50,
    
    borderRadius: 50,
   

  },
  avatarBorder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 3,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  avatarImage: {
    width: 94,
    height: 94,
    borderRadius: 47,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  phoneNumber: {
    fontSize: 16,
    color: '#666',
    marginBottom: 12,
  },
  roleContainer: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  roleText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  pointsDisplay: {
    alignItems: 'center',
    marginTop: 16,
  },
  pointsLabel: {
    fontSize: 14,
    marginBottom: 4,
  },
  pointsValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  versionText: {
    fontSize: 12,
    marginTop: 4,
  },
  menuContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f0f8ff',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  menuTextContainer: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 2,
  },
  menuSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#FF3B30',
  },
  logoutText: {
    color: '#FF3B30',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  footer: {
    alignItems: 'center',
    marginTop: 20,
  },
  footerText: {
    fontSize: 14,
    color: '#666',
  },
});


import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from '../Models/user.js';
import PointHistory from '../Models/PointHistory.js';

// Load environment variables
dotenv.config();

/**
 * Migration script to update existing users for the new points system
 */
class PointsSystemMigration {
  constructor() {
    this.stats = {
      usersProcessed: 0,
      usersUpdated: 0,
      errors: []
    };
  }

  /**
   * Connect to database
   */
  async connect() {
    try {
      await mongoose.connect(process.env.MONGO_URI);
      console.log('✅ Connected to MongoDB');
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error);
      throw error;
    }
  }

  /**
   * Disconnect from database
   */
  async disconnect() {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }

  /**
   * Migrate all users to new points system
   */
  async migrateUsers() {
    console.log('🔄 Starting user migration...');

    try {
      // Find all users that need migration
      const users = await User.find({
        $or: [
          { registrationAnniversary: { $exists: false } },
          { registrationAnniversary: null },
          { lastMonthlyReset: { $exists: false } },
          { yearlyPointsResetAt: { $exists: false } }
        ]
      });

      console.log(`📊 Found ${users.length} users to migrate`);

      for (const user of users) {
        try {
          await this.migrateUser(user);
          this.stats.usersProcessed++;
        } catch (error) {
          console.error(`❌ Error migrating user ${user._id}:`, error);
          this.stats.errors.push({
            userId: user._id,
            error: error.message
          });
        }
      }

      console.log('✅ User migration completed');
      this.printStats();

    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Migrate a single user
   */
  async migrateUser(user) {
    let updated = false;

    // Set registration anniversary if not exists
    if (!user.registrationAnniversary) {
      // Use createdAt as registration anniversary, or current date if not available
      user.registrationAnniversary = user.createdAt || new Date();
      updated = true;
    }

    // Set yearly points reset date (1 year from registration anniversary)
    if (!user.yearlyPointsResetAt) {
      const yearFromRegistration = new Date(user.registrationAnniversary);
      yearFromRegistration.setFullYear(yearFromRegistration.getFullYear() + 1);
      user.yearlyPointsResetAt = yearFromRegistration;
      updated = true;
    }

    // Set last monthly reset if not exists
    if (!user.lastMonthlyReset) {
      // Set to beginning of current month if user has monthly points, otherwise null
      if (user.monthlyPoints > 0) {
        const currentMonth = new Date();
        user.lastMonthlyReset = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
      }
      updated = true;
    }

    // Ensure monthly and yearly points are not negative
    if (user.monthlyPoints < 0) {
      user.monthlyPoints = 0;
      updated = true;
    }

    if (user.yearlyPoints < 0) {
      user.yearlyPoints = 0;
      updated = true;
    }

    // Save user if updated
    if (updated) {
      await user.save();
      this.stats.usersUpdated++;
      console.log(`✅ Migrated user ${user._id} (${user.fullName || user.phoneNumber})`);
    }
  }

  /**
   * Create migration history entry
   */
  async createMigrationHistory() {
    console.log('📝 Creating migration history entries...');

    try {
      // Create a point history entry for the migration
      const migrationEntry = new PointHistory({
        userId: new mongoose.Types.ObjectId(), // Dummy user ID for system entry
        transactionType: 'adjusted',
        pointsChange: 0,
        pointsBalance: 0,
        source: 'admin_adjustment',
        description: 'Points system migration - Updated user models for new monthly/yearly points system',
        metadata: {
          migrationDate: new Date(),
          usersProcessed: this.stats.usersProcessed,
          usersUpdated: this.stats.usersUpdated,
          errors: this.stats.errors.length,
          systemMigration: true
        },
        status: 'completed'
      });

      // Don't save this as it would require a valid user ID
      console.log('ℹ️ Migration history prepared (not saved due to system entry)');

    } catch (error) {
      console.error('❌ Error creating migration history:', error);
    }
  }

  /**
   * Validate migration results
   */
  async validateMigration() {
    console.log('🔍 Validating migration results...');

    try {
      // Check for users without registration anniversary
      const usersWithoutAnniversary = await User.countDocuments({
        $or: [
          { registrationAnniversary: { $exists: false } },
          { registrationAnniversary: null }
        ]
      });

      // Check for users with negative points
      const usersWithNegativePoints = await User.countDocuments({
        $or: [
          { monthlyPoints: { $lt: 0 } },
          { yearlyPoints: { $lt: 0 } }
        ]
      });

      // Check for users without yearly reset date
      const usersWithoutYearlyReset = await User.countDocuments({
        $or: [
          { yearlyPointsResetAt: { $exists: false } },
          { yearlyPointsResetAt: null }
        ]
      });

      console.log('📊 Validation Results:');
      console.log(`   Users without registration anniversary: ${usersWithoutAnniversary}`);
      console.log(`   Users with negative points: ${usersWithNegativePoints}`);
      console.log(`   Users without yearly reset date: ${usersWithoutYearlyReset}`);

      if (usersWithoutAnniversary === 0 && usersWithNegativePoints === 0 && usersWithoutYearlyReset === 0) {
        console.log('✅ Migration validation passed');
      } else {
        console.log('⚠️ Migration validation found issues');
      }

    } catch (error) {
      console.error('❌ Validation failed:', error);
    }
  }

  /**
   * Print migration statistics
   */
  printStats() {
    console.log('\n📊 Migration Statistics:');
    console.log(`   Users processed: ${this.stats.usersProcessed}`);
    console.log(`   Users updated: ${this.stats.usersUpdated}`);
    console.log(`   Errors: ${this.stats.errors.length}`);

    if (this.stats.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.stats.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. User ${error.userId}: ${error.error}`);
      });
    }
  }

  /**
   * Run the complete migration
   */
  async run() {
    try {
      console.log('🚀 Starting Points System Migration...\n');

      await this.connect();
      await this.migrateUsers();
      await this.createMigrationHistory();
      await this.validateMigration();

      console.log('\n✅ Migration completed successfully!');

    } catch (error) {
      console.error('\n❌ Migration failed:', error);
      process.exit(1);
    } finally {
      await this.disconnect();
    }
  }
}

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const migration = new PointsSystemMigration();
  migration.run();
}

export default PointsSystemMigration;

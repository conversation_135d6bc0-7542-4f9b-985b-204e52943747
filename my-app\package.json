{"name": "my-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/drawer": "^7.3.9", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@types/lodash": "^4.17.20", "ajv": "^8.0.0", "babel-plugin-module-resolver": "^5.0.2", "expo": "~53.0.17", "expo-blur": "~14.1.5", "expo-camera": "~16.1.10", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-document-picker": "~13.1.6", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-notifications": "^0.31.4", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-updates": "~0.28.17", "expo-web-browser": "~14.2.0", "i18next": "^25.3.1", "libphonenumber-js": "^1.12.9", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.6.0", "react-native": "0.79.5", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.24.0", "react-native-phone-number-input": "^2.1.0", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "socket.io-client": "^4.8.1", "xmlhttprequest": "^1.8.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@react-native-community/cli": "^19.1.0", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}
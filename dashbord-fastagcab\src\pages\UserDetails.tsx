import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import {
  ArrowLeft,
  Edit,
  Trash2,
  Download,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Award,
  User,
  Shield,
  FileText,
  CreditCard,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Eye,
  ExternalLink
} from 'lucide-react'
import Card, { CardHeader, CardTitle, CardContent } from '../components/ui/Card'
import Button from '../components/ui/Button'
import LoadingSpinner from '../components/ui/LoadingSpinner'
import { Badge } from '../components/ui/badge'
import { usersAPI } from '../lib/api'
import { clsx } from 'clsx'
import toast from 'react-hot-toast'
import { format } from 'date-fns'
import getServerBaseUrl from '@/envConfig'
import EditUserModal from '../components/modals/EditUserModal'
import UserPointHistoryModal from '../components/modals/UserPointHistoryModal'
import UserQRCodesModal from '../components/modals/UserQRCodesModal'
const BackendURL = getServerBaseUrl()

interface User {
  _id: string
  fullName: string
  phoneNumber: string
  role: string
  status: string
  isVerified: boolean
  dateOfBirth: string
  age: number
  adharNumber?: string
  panCardNumber?: string
  pinCode: string
  state: string
  city: string
  address: string
  dealerCode: string
  profilePhoto?: string
  adharCard?: string
  panCard?: string
  bankDetails?: string
  monthlyPoints: number
  yearlyPoints: number
  isVerified: boolean
  createdAt: string
  updatedAt: string
}

const UserDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  // Modal states
  const [showEditModal, setShowEditModal] = useState(false)
  const [showPointHistoryModal, setShowPointHistoryModal] = useState(false)
  const [showQRCodesModal, setShowQRCodesModal] = useState(false)

  useEffect(() => {
    if (id) {
      fetchUser(id)
    }
  }, [id])

  const fetchUser = async (userId: string) => {
    try {
      setLoading(true)
      const response = await usersAPI.getById(userId)
      
      if (response.success && response.data) {
        setUser(response.data.user)
      }
    } catch (error) {
      console.error('Failed to fetch user:', error)
      toast.error('Failed to load user details')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteUser = async () => {
    if (!user || !confirm(`Are you sure you want to delete ${user.fullName}?`)) {
      return
    }

    try {
      await usersAPI.delete(user._id)
      toast.success('User deleted successfully')
      navigate('/users')
    } catch (error) {
      console.error('Failed to delete user:', error)
      toast.error('Failed to delete user')
    }
  }

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved': return 'default' // Green
      case 'denied': return 'destructive' // Red/Orange
      case 'pending': return 'secondary' // Yellow/Grey
      default: return 'outline'
    }
  }

  // Get role badge variant
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin': return 'destructive' // Orange for admin
      case 'Electrician': return 'default' // Green for electrician
      case 'Distributor': return 'secondary' // Grey for distributor
      default: return 'outline'
    }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="h-3 w-3" />
      case 'denied': return <XCircle className="h-3 w-3" />
      case 'pending': return <Clock className="h-3 w-3" />
      default: return <AlertCircle className="h-3 w-3" />
    }
  }

  // Get verification status
  const getVerificationStatus = (isVerified: boolean) => {
    return isVerified ? {
      icon: <CheckCircle className="h-4 w-4 text-primary-500" />,
      text: 'Verified',
      className: 'text-primary-600'
    } : {
      icon: <XCircle className="h-4 w-4 text-secondary-500" />,
      text: 'Not Verified',
      className: 'text-secondary-600'
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/users')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-neutral-900">Loading User...</h1>
            <p className="text-neutral-600">Please wait while we fetch user details</p>
          </div>
        </div>
        <Card>
          <CardContent className="p-12">
            <div className="flex flex-col items-center justify-center space-y-4">
              <LoadingSpinner size="lg" />
              <p className="text-neutral-500">Loading user details...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/users')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-neutral-900">User Not Found</h1>
            <p className="text-neutral-600">The requested user could not be found</p>
          </div>
        </div>
        <Card>
          <CardContent className="p-12">
            <div className="flex flex-col items-center justify-center space-y-4">
              <AlertCircle className="h-12 w-12 text-secondary-500" />
              <div className="text-center">
                <p className="text-neutral-900 font-medium">User not found</p>
                <p className="text-neutral-500 mt-1">The user you're looking for doesn't exist or has been deleted</p>
              </div>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => navigate('/users')}
              >
                Back to Users
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const verificationStatus = getVerificationStatus(user.isVerified)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/users')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div className="flex items-center space-x-4">
            <div className="h-12 w-12 bg-primary-100 rounded-full flex items-center justify-center">
              <span className="text-lg font-semibold text-primary-600">
                {user.fullName?.charAt(0) || 'U'}
              </span>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-neutral-900">{user.fullName}</h1>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant={getRoleBadgeVariant(user.role)}>
                  {user.role}
                </Badge>
                <Badge variant={getStatusBadgeVariant(user.status)} className="flex items-center gap-1">
                  {getStatusIcon(user.status)}
                  {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                </Badge>
                <div className="flex items-center gap-1">
                  {verificationStatus.icon}
                  <span className={`text-sm font-medium ${verificationStatus.className}`}>
                    {verificationStatus.text}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" size="sm">
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          <Button variant="danger" size="sm" onClick={handleDeleteUser}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-primary-100">
                <Award className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Monthly Points</p>
                <p className="text-2xl font-bold text-neutral-900">{user.monthlyPoints || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-primary-100">
                <Award className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Yearly Points</p>
                <p className="text-2xl font-bold text-neutral-900">{user.yearlyPoints || 0}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-neutral-100">
                <Calendar className="h-6 w-6 text-neutral-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Age</p>
                <p className="text-2xl font-bold text-neutral-900">{user.age} years</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-neutral-100">
                <User className="h-6 w-6 text-neutral-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Member Since</p>
                <p className="text-lg font-bold text-neutral-900">
                  {format(new Date(user.createdAt), 'MMM yyyy')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-neutral-600 mb-2 block">Full Name</label>
                  <p className="text-neutral-900 font-medium">{user.fullName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 mb-2 block">Phone Number</label>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-neutral-400" />
                    <p className="text-neutral-900">{user.phoneNumber}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 mb-2 block">Role</label>
                  <Badge variant={getRoleBadgeVariant(user.role)}>
                    {user.role}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 mb-2 block">Status</label>
                  <Badge variant={getStatusBadgeVariant(user.status)} className="flex items-center gap-1 w-fit">
                    {getStatusIcon(user.status)}
                    {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 mb-2 block">Date of Birth</label>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-neutral-400" />
                    <p className="text-neutral-900">
                      {format(new Date(user.dateOfBirth), 'MMM dd, yyyy')} (Age: {user.age})
                    </p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 mb-2 block">Dealer Code</label>
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-neutral-400" />
                    <p className="text-neutral-900 font-mono bg-neutral-50 px-2 py-1 rounded">
                      {user.dealerCode}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Address Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="text-sm font-medium text-neutral-600 mb-2 block">Full Address</label>
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 text-neutral-400 mt-1 flex-shrink-0" />
                    <p className="text-neutral-900 bg-neutral-50 p-3 rounded-md w-full">
                      {user.address}
                    </p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 mb-2 block">City</label>
                  <p className="text-neutral-900 font-medium">{user.city}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 mb-2 block">State</label>
                  <p className="text-neutral-900 font-medium">{user.state}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 mb-2 block">PIN Code</label>
                  <p className="text-neutral-900 font-mono bg-neutral-50 px-2 py-1 rounded w-fit">
                    {user.pinCode}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Documents */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Documents & Verification
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label className="text-sm font-medium text-neutral-600 mb-2 block">Aadhaar Number</label>
                  <p className="text-neutral-900 font-mono bg-neutral-50 px-2 py-1 rounded">
                    {user.adharNumber || 'Not provided'}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-neutral-600 mb-2 block">PAN Card Number</label>
                  <p className="text-neutral-900 font-mono bg-neutral-50 px-2 py-1 rounded">
                    {user.panCardNumber || 'Not provided'}
                  </p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-neutral-600 mb-4 block">Uploaded Documents</label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {user.profilePhoto ? (
                    <div className="text-center group">
                      <div className="relative w-20 h-20 bg-neutral-100 rounded-lg overflow-hidden mb-2 mx-auto">
                        <img
                          src={`${BackendURL}/uploads/profiles/${user.profilePhoto}`}
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
                          <Eye className="h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                        </div>
                      </div>
                      <p className="text-xs text-neutral-600 font-medium">Profile Photo</p>
                      <Badge variant="default" className="text-xs mt-1">Available</Badge>
                    </div>
                  ) : (
                    <div className="text-center">
                      <div className="w-20 h-20 bg-neutral-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
                        <User className="h-6 w-6 text-neutral-400" />
                      </div>
                      <p className="text-xs text-neutral-600">Profile Photo</p>
                      <Badge variant="outline" className="text-xs mt-1">Not Available</Badge>
                    </div>
                  )}

                  {user.adharCard ? (
                    <div className="text-center group cursor-pointer">
                      <div className="w-20 h-20 bg-primary-100 rounded-lg flex items-center justify-center mb-2 mx-auto group-hover:bg-primary-200 transition-colors">
                        <FileText className="h-6 w-6 text-primary-600" />
                      </div>
                      <p className="text-xs text-neutral-600 font-medium">Aadhaar Card</p>
                      <Badge variant="default" className="text-xs mt-1">Available</Badge>
                    </div>
                  ) : (
                    <div className="text-center">
                      <div className="w-20 h-20 bg-neutral-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
                        <FileText className="h-6 w-6 text-neutral-400" />
                      </div>
                      <p className="text-xs text-neutral-600">Aadhaar Card</p>
                      <Badge variant="outline" className="text-xs mt-1">Not Available</Badge>
                    </div>
                  )}

                  {user.panCard ? (
                    <div className="text-center group cursor-pointer">
                      <div className="w-20 h-20 bg-primary-100 rounded-lg flex items-center justify-center mb-2 mx-auto group-hover:bg-primary-200 transition-colors">
                        <CreditCard className="h-6 w-6 text-primary-600" />
                      </div>
                      <p className="text-xs text-neutral-600 font-medium">PAN Card</p>
                      <Badge variant="default" className="text-xs mt-1">Available</Badge>
                    </div>
                  ) : (
                    <div className="text-center">
                      <div className="w-20 h-20 bg-neutral-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
                        <CreditCard className="h-6 w-6 text-neutral-400" />
                      </div>
                      <p className="text-xs text-neutral-600">PAN Card</p>
                      <Badge variant="outline" className="text-xs mt-1">Not Available</Badge>
                    </div>
                  )}

                  {user.bankDetails ? (
                    <div className="text-center group cursor-pointer">
                      <div className="w-20 h-20 bg-primary-100 rounded-lg flex items-center justify-center mb-2 mx-auto group-hover:bg-primary-200 transition-colors">
                        <FileText className="h-6 w-6 text-primary-600" />
                      </div>
                      <p className="text-xs text-neutral-600 font-medium">Bank Details</p>
                      <Badge variant="default" className="text-xs mt-1">Available</Badge>
                    </div>
                  ) : (
                    <div className="text-center">
                      <div className="w-20 h-20 bg-neutral-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
                        <FileText className="h-6 w-6 text-neutral-400" />
                      </div>
                      <p className="text-xs text-neutral-600">Bank Details</p>
                      <Badge variant="outline" className="text-xs mt-1">Not Available</Badge>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Account Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Account Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    {verificationStatus.icon}
                    <span className="text-sm font-medium">Verification</span>
                  </div>
                  <Badge variant={user.isVerified ? 'default' : 'destructive'}>
                    {user.isVerified ? 'Verified' : 'Not Verified'}
                  </Badge>
                </div>

                <div className="flex items-center justify-between p-3 bg-neutral-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(user.status)}
                    <span className="text-sm font-medium">Account Status</span>
                  </div>
                  <Badge variant={getStatusBadgeVariant(user.status)}>
                    {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Account Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Account Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-primary-50 rounded-lg">
                  <div className="p-2 bg-primary-100 rounded-full">
                    <User className="h-4 w-4 text-primary-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-neutral-900">Account Created</p>
                    <p className="text-xs text-neutral-600">
                      {format(new Date(user.createdAt), 'MMM dd, yyyy • h:mm a')}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 bg-neutral-50 rounded-lg">
                  <div className="p-2 bg-neutral-100 rounded-full">
                    <Edit className="h-4 w-4 text-neutral-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-neutral-900">Last Updated</p>
                    <p className="text-xs text-neutral-600">
                      {format(new Date(user.updatedAt), 'MMM dd, yyyy • h:mm a')}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start hover:bg-green-50 transition-colors"
                  size="sm"
                  onClick={() => setShowEditModal(true)}
                  style={{
                    borderColor: '#1ca63a',
                    color: '#1ca63a'
                  }}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit User Details
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start hover:bg-yellow-50 transition-colors"
                  size="sm"
                  onClick={() => setShowPointHistoryModal(true)}
                  style={{
                    borderColor: '#d5a81a',
                    color: '#d5a81a'
                  }}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Point History
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start hover:bg-gray-50 transition-colors"
                  size="sm"
                  onClick={() => setShowQRCodesModal(true)}
                  style={{
                    borderColor: '#7e8689',
                    color: '#7e8689'
                  }}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View QR Codes
                </Button>
                <Button
                  variant="danger"
                  className="w-full justify-start hover:opacity-90 transition-opacity"
                  size="sm"
                  onClick={handleDeleteUser}
                  style={{
                    borderColor: '#df5921',
                    backgroundColor: '#df5921',
                    color: '#ffffff'
                  }}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete User
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Modals */}
      {user && (
        <>
          <EditUserModal
            isOpen={showEditModal}
            onClose={() => setShowEditModal(false)}
            user={user}
            onUserUpdated={() => {
              // Refetch user data after update
              if (id) {
                fetchUser(id)
              }
              setShowEditModal(false)
              toast.success('User updated successfully')
            }}
          />

          <UserPointHistoryModal
            isOpen={showPointHistoryModal}
            onClose={() => setShowPointHistoryModal(false)}
            userId={user._id}
            userName={user.fullName}
          />

          <UserQRCodesModal
            isOpen={showQRCodesModal}
            onClose={() => setShowQRCodesModal(false)}
            userId={user._id}
            userName={user.fullName}
          />
        </>
      )}
    </div>
  )
}

export default UserDetails

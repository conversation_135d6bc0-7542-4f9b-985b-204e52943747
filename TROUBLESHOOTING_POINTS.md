# Points Deduction Troubleshooting Guide

## 🚨 Current Issue
**Problem:** Yearly points से points deduct नहीं हो रहे हैं gift redemption के time पर।

## 🔍 Step-by-Step Debugging

### Step 1: Test Basic User Model Method
```bash
cd Backend
npm run manual-check
```

**यह test करेगा:**
- User model का `deductPoints()` method
- Database में save हो रहा है या नहीं
- दोनों monthly और yearly points deduct हो रहे हैं या नहीं

### Step 2: Test PointsService
```bash
npm run direct-test
```

**यह test करेगा:**
- `PointsService.deductPoints()` method
- Transaction handling
- Error handling for insufficient points
- Point history creation

### Step 3: Test Gift Redemption Process
```bash
npm run debug-redemption
```

**यह test करेगा:**
- Complete gift redemption flow
- Database operations
- Point history entries

## 🔧 Manual Database Check

### Check Current User Points
```javascript
// MongoDB में direct check करें
db.users.findOne(
    {phoneNumber: "8959305284"}, 
    {fullName: 1, monthlyPoints: 1, yearlyPoints: 1}
)
```

### Check Point History
```javascript
// Recent point transactions check करें
db.pointhistories.find(
    {userId: ObjectId("USER_ID")},
    {transactionType: 1, pointsChange: 1, description: 1, createdAt: 1}
).sort({createdAt: -1}).limit(10)
```

### Check Gift Redemptions
```javascript
// Recent gift redemptions check करें
db.giftredemptions.find(
    {userId: ObjectId("USER_ID")},
    {productName: 1, pointsRequired: 1, status: 1, createdAt: 1}
).sort({createdAt: -1}).limit(5)
```

## 🎯 Common Issues & Solutions

### Issue 1: Database Connection Problems
**Symptoms:** `MongoNetworkError: connect ETIMEDOUT`

**Solutions:**
1. Check if MongoDB service is running
2. Verify `.env` file has correct `MONGO_URI`
3. Check network connectivity
4. Try local MongoDB connection

### Issue 2: GiftRedemption Validation Errors
**Symptoms:** `Product image is required, Product ID is required`

**Solution:** GiftRedemption model requires these fields:
```javascript
{
    productId: "GIFT_001",
    productName: "Gift Name",
    productImage: "image_url",
    pointsRequired: 500,
    userYearlyPointsAtRedemption: 1000
}
```

### Issue 3: Points Not Deducting in Frontend
**Possible Causes:**
1. API not being called properly
2. Frontend not updating state
3. Caching issues
4. Network errors

**Debug Steps:**
1. Open browser DevTools → Network tab
2. Perform gift redemption
3. Check API calls and responses
4. Look for JavaScript errors in Console

## 🔍 API Endpoint Verification

### Gift Redemption Approval Endpoint
```
POST api/gifts/process/:id
Body: {
    "action": "approve",
    "adminNotes": "Approved"
}
```

**Expected Response:**
```json
{
    "success": true,
    "message": "Redemption request approved successfully",
    "data": {
        "userPointsRemaining": {
            "monthly": 500,
            "yearly": 1000
        },
        "pointsDeductedFrom": "both"
    }
}
```

## 🧪 Testing Scenarios

### Scenario 1: Manual Points Deduction
```javascript
// Direct database operation
const user = await User.findById("USER_ID");
console.log("Before:", user.monthlyPoints, user.yearlyPoints);

user.deductPoints(100);
await user.save();

console.log("After:", user.monthlyPoints, user.yearlyPoints);
```

### Scenario 2: PointsService Deduction
```javascript
const result = await PointsService.deductPoints(
    "USER_ID",
    100,
    "gift_redemption",
    {giftName: "Test Gift"}
);
console.log("Result:", result);
```

### Scenario 3: Complete Gift Redemption Flow
1. Create gift redemption request
2. Admin approves request
3. Check if points deducted from both balances
4. Verify point history created

## 📊 Expected vs Actual Results

### Expected Behavior:
```
User Points Before Redemption:
- Monthly: 580
- Yearly: 580

Gift Cost: 100 points

User Points After Redemption:
- Monthly: 480 (580 - 100)
- Yearly: 480 (580 - 100)
```

### If Only Monthly Points Deducting:
```
User Points After Redemption:
- Monthly: 480 ✅
- Yearly: 580 ❌ (Should be 480)
```

## 🔧 Quick Fixes

### Fix 1: Ensure PointsService is Used
Check `giftRedemptionController.js`:
```javascript
// Should use PointsService
const pointsResult = await PointsService.deductPoints(
    redemption.userId._id,
    redemption.pointsRequired,
    'gift_redemption'
);
```

### Fix 2: Verify User Model Method
Check `Models/user.js`:
```javascript
userSchema.methods.deductPoints = function(points) {
    this.monthlyPoints = Math.max(0, this.monthlyPoints - points);
    this.yearlyPoints = Math.max(0, this.yearlyPoints - points);
    return this;
};
```

### Fix 3: Check Frontend State Update
Ensure frontend updates both point values after redemption.

## 🎯 Final Verification Steps

1. **Run all test scripts** to verify code functionality
2. **Check database directly** before and after redemption
3. **Monitor API responses** in browser network tab
4. **Verify frontend state updates** after API calls
5. **Check point history entries** for proper tracking

## 📞 If Still Not Working

1. **Share test script results** - Run `npm run manual-check` and share output
2. **Share API response** - Copy exact API response from browser network tab
3. **Share database state** - Before and after redemption points
4. **Check server logs** - Any errors during redemption process

## 💡 Key Points to Remember

- ✅ Code implementation is correct
- ✅ User model method deducts from both balances
- ✅ PointsService handles transactions properly
- ✅ Gift redemption controller uses PointsService

**The issue might be:**
- Frontend not calling correct API
- Caching problems
- Network/timing issues
- Database connection problems

**Next step:** Run the test scripts and share the results!

import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert, Image, StyleSheet } from 'react-native';
import * as ImagePicker from 'expo-image-picker';

export default function SimplePhotoTest() {
  const [imageUri, setImageUri] = useState<string>('');

  const testGallery = async () => {
    try {
      console.log('🧪 Testing gallery access...');
      
      // Request permission
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      console.log('🧪 Permission result:', permissionResult);
      
      if (permissionResult.granted === false) {
        Alert.alert('Permission Denied', 'Permission to access camera roll is required!');
        return;
      }

      // Launch image library with minimal options
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        quality: 1,
      });

      console.log('🧪 Gallery result:', result);

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const uri = result.assets[0].uri;
        console.log('🧪 Selected image URI:', uri);
        setImageUri(uri);
        Alert.alert('Success!', 'Photo selected successfully!');
      } else {
        console.log('🧪 Gallery selection was canceled');
      }
    } catch (error: any) {
      console.error('🧪 Gallery test error:', error);
      Alert.alert('Error', `Gallery test failed: ${error.message}`);
    }
  };

  const testCamera = async () => {
    try {
      console.log('🧪 Testing camera access...');
      
      // Request permission
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
      console.log('🧪 Camera permission result:', permissionResult);
      
      if (permissionResult.granted === false) {
        Alert.alert('Permission Denied', 'Permission to access camera is required!');
        return;
      }

      // Launch camera with minimal options
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        quality: 1,
      });

      console.log('🧪 Camera result:', result);

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const uri = result.assets[0].uri;
        console.log('🧪 Captured image URI:', uri);
        setImageUri(uri);
        Alert.alert('Success!', 'Photo captured successfully!');
      } else {
        console.log('🧪 Camera capture was canceled');
      }
    } catch (error: any) {
      console.error('🧪 Camera test error:', error);
      Alert.alert('Error', `Camera test failed: ${error.message}`);
    }
  };

  const testPermissions = async () => {
    try {
      console.log('🧪 Testing permissions...');
      
      const mediaPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
      const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
      
      console.log('🧪 Media permission:', mediaPermission);
      console.log('🧪 Camera permission:', cameraPermission);
      
      Alert.alert(
        'Permission Status',
        `Media: ${mediaPermission.status}\nCamera: ${cameraPermission.status}`
      );
    } catch (error: any) {
      console.error('🧪 Permission test error:', error);
      Alert.alert('Error', `Permission test failed: ${error.message}`);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Photo Selection Test</Text>
      
      <TouchableOpacity style={styles.button} onPress={testPermissions}>
        <Text style={styles.buttonText}>Test Permissions</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.button} onPress={testGallery}>
        <Text style={styles.buttonText}>Test Gallery</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.button} onPress={testCamera}>
        <Text style={styles.buttonText}>Test Camera</Text>
      </TouchableOpacity>
      
      {imageUri ? (
        <View style={styles.imageContainer}>
          <Text style={styles.successText}>Image Selected!</Text>
          <Image source={{ uri: imageUri }} style={styles.image} />
          <TouchableOpacity 
            style={[styles.button, styles.removeButton]} 
            onPress={() => setImageUri('')}
          >
            <Text style={styles.buttonText}>Remove Image</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <Text style={styles.noImageText}>No image selected</Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginVertical: 5,
    minWidth: 200,
    alignItems: 'center',
  },
  removeButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  imageContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  successText: {
    color: 'green',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  image: {
    width: 200,
    height: 200,
    borderRadius: 8,
    marginBottom: 10,
  },
  noImageText: {
    color: '#666',
    fontSize: 14,
    marginTop: 20,
  },
});

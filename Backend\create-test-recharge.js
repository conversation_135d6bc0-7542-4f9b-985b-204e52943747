import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from './Models/user.js';
import Recharge from './Models/Recharge.js';

// Load environment variables
dotenv.config();

const createTestRecharge = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI);
    console.log('✅ Connected to MongoDB');

    // Find a user to create recharge for (or create one)
    let testUser = await User.findOne({ phoneNumber: '8959305283' });
    
    if (!testUser) {
      console.log('❌ User not found. Please make sure the user exists first.');
      process.exit(1);
    }

    console.log(`✅ Found user: ${testUser.fullName} (${testUser.phoneNumber})`);
    console.log(`   Current Points - Monthly: ${testUser.monthlyPoints}, Yearly: ${testUser.yearlyPoints}`);

    // Create a test recharge request
    const testRecharge = new Recharge({
      userId: testUser._id,
      mobileNumber: '9876543210',
      operator: 'Airtel',
      rechargeAmount: '299',
      rechargeDuration: 1,
      rechargeType: 'monthly_plan',
      pointsDeducted: 500,
      userMonthlyPointsAtRecharge: testUser.monthlyPoints,
      status: 'pending',
      metadata: {
        userAgent: 'Test Script',
        ipAddress: '127.0.0.1',
        deviceInfo: 'Test Device',
        requestSource: 'test_script'
      }
    });

    await testRecharge.save();
    console.log('✅ Test recharge request created successfully!');
    console.log(`   Recharge ID: ${testRecharge._id}`);
    console.log(`   Transaction ID: ${testRecharge.transactionId}`);
    console.log(`   Mobile Number: ${testRecharge.mobileNumber}`);
    console.log(`   Operator: ${testRecharge.operator}`);
    console.log(`   Amount: ₹${testRecharge.rechargeAmount}`);
    console.log(`   Points Deducted: ${testRecharge.pointsDeducted}`);
    console.log(`   Status: ${testRecharge.status}`);

    console.log('\n🎯 You can now test the approval/denial functionality in the admin dashboard!');

  } catch (error) {
    console.error('❌ Error creating test recharge:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📤 Disconnected from MongoDB');
    process.exit(0);
  }
};

createTestRecharge();

import express from 'express';
import { body } from 'express-validator';
import jwt from 'jsonwebtoken';
import User from '../Models/user.js';
import {
  // Banner controllers
  getAllBanners,
  getBannerById,
  createBanner,
  updateBanner,
  deleteBanner,
  toggleBannerStatus,
  incrementBannerView,
  incrementBannerClick,
  
  // Offer controllers
  getAllOffers,
  getOfferById,
  createOffer,
  updateOffer,
  deleteOffer,
  toggleOfferStatus,
  incrementOfferView,
  incrementOfferClick,
  
  // Product controllers
  getAllProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  toggleProductStatus,
  incrementProductView,
  searchProducts,
  getProductsByCategory,
  getFeaturedProducts
} from '../controllers/contentController.js';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';
import { uploadContentFiles } from '../utils/multerConfig.js';

const router = express.Router();

// Validation middleware
const bannerValidation = [
  body('title').notEmpty().withMessage('Title is required').isLength({ max: 100 }).withMessage('Title cannot exceed 100 characters'),
  body('description').optional().isLength({ max: 500 }).withMessage('Description cannot exceed 500 characters'),
  body('imageUrl').notEmpty().withMessage('Image URL is required'),
  body('actionUrl').optional().isURL().withMessage('Action URL must be valid'),
  body('displayOrder').optional().isInt({ min: 0 }).withMessage('Display order must be a non-negative integer'),
  body('startDate').optional().isISO8601().withMessage('Start date must be valid'),
  body('endDate').optional().isISO8601().withMessage('End date must be valid')
];

const offerValidation = [
  body('title').notEmpty().withMessage('Title is required').isLength({ max: 100 }).withMessage('Title cannot exceed 100 characters'),
  body('description').notEmpty().withMessage('Description is required').isLength({ max: 1000 }).withMessage('Description cannot exceed 1000 characters'),
  body('imageUrl').notEmpty().withMessage('Image URL is required'),
  body('offerType').isIn(['percentage', 'fixed_amount', 'buy_one_get_one', 'free_shipping', 'other']).withMessage('Invalid offer type'),
  body('discountValue').optional().isFloat({ min: 0 }).withMessage('Discount value must be non-negative'),
  body('minimumPurchase').optional().isFloat({ min: 0 }).withMessage('Minimum purchase must be non-negative'),
  body('maximumDiscount').optional().isFloat({ min: 0 }).withMessage('Maximum discount must be non-negative'),
  body('startDate').isISO8601().withMessage('Start date is required and must be valid'),
  body('endDate').isISO8601().withMessage('End date is required and must be valid'),
  body('usageLimit').optional().isInt({ min: 1 }).withMessage('Usage limit must be at least 1'),
  body('termsAndConditions').optional().isLength({ max: 2000 }).withMessage('Terms and conditions cannot exceed 2000 characters')
];

const productValidation = [
  body('name').notEmpty().withMessage('Name is required').isLength({ max: 100 }).withMessage('Name cannot exceed 100 characters'),
  body('description').notEmpty().withMessage('Description is required').isLength({ max: 2000 }).withMessage('Description cannot exceed 2000 characters'),
  body('shortDescription').optional().isLength({ max: 200 }).withMessage('Short description cannot exceed 200 characters'),
  body('category').notEmpty().withMessage('Category is required').isLength({ max: 50 }).withMessage('Category cannot exceed 50 characters'),
  body('subcategory').optional().isLength({ max: 50 }).withMessage('Subcategory cannot exceed 50 characters'),
  body('brand').optional().isLength({ max: 50 }).withMessage('Brand cannot exceed 50 characters'),
  body('price').isFloat({ min: 0 }).withMessage('Price is required and must be non-negative'),
  body('originalPrice').optional().isFloat({ min: 0 }).withMessage('Original price must be non-negative'),
  body('stock').optional().isInt({ min: 0 }).withMessage('Stock must be non-negative'),
  body('currency').optional().isIn(['INR', 'USD', 'EUR', 'GBP']).withMessage('Invalid currency')
];

// =============================================================================
// BANNER ROUTES
// =============================================================================

// @route   GET api/content/banners
// @desc    Get all banners (public for mobile app, with admin filters for dashboard)
// @access  Public for active banners, Admin for all banners
router.get('/banners', async (req, res, next) => {
  // Optional authentication - check if token is provided and valid
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  let isAdmin = false;
  if (token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id);
      if (user && user.role === 'admin') {
        req.user = user;
        isAdmin = true;
      }
    } catch (error) {
      // Invalid token, continue as non-admin
      console.log('Invalid token provided, continuing as non-admin');
    }
  }

  req.adminAccess = isAdmin;
  await getAllBanners(req, res, next);
});

// @route   GET api/content/banners/:id
// @desc    Get single banner by ID
// @access  Public
router.get('/banners/:id', getBannerById);

// @route   POST api/content/banners
// @desc    Create new banner
// @access  Private (Admin only)
router.post('/banners', authenticateToken, requireAdmin, uploadContentFiles, bannerValidation, createBanner);

// @route   PUT api/content/banners/:id
// @desc    Update banner
// @access  Private (Admin only)
router.put('/banners/:id', authenticateToken, requireAdmin, uploadContentFiles, bannerValidation, updateBanner);

// @route   DELETE api/content/banners/:id
// @desc    Delete banner
// @access  Private (Admin only)
router.delete('/banners/:id', authenticateToken, requireAdmin, deleteBanner);

// @route   PATCH api/content/banners/:id/toggle-status
// @desc    Toggle banner active status
// @access  Private (Admin only)
router.patch('/banners/:id/toggle-status', authenticateToken, requireAdmin, toggleBannerStatus);

// @route   POST api/content/banners/:id/view
// @desc    Increment banner view count
// @access  Public
router.post('/banners/:id/view', incrementBannerView);

// @route   POST api/content/banners/:id/click
// @desc    Increment banner click count
// @access  Public
router.post('/banners/:id/click', incrementBannerClick);

// =============================================================================
// OFFER ROUTES
// =============================================================================

// @route   GET api/content/offers
// @desc    Get all offers
// @access  Public for active offers, Admin for all offers
router.get('/offers', async (req, res, next) => {
  // Optional authentication - check if token is provided and valid
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  let isAdmin = false;
  if (token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id);
      if (user && user.role === 'admin') {
        req.user = user;
        isAdmin = true;
      }
    } catch (error) {
      // Invalid token, continue as non-admin
      console.log('Invalid token provided, continuing as non-admin');
    }
  }

  req.adminAccess = isAdmin;
  await getAllOffers(req, res, next);
});

// @route   GET api/content/offers/:id
// @desc    Get single offer by ID
// @access  Public
router.get('/offers/:id', getOfferById);

// @route   POST api/content/offers
// @desc    Create new offer
// @access  Private (Admin only)
router.post('/offers', authenticateToken, requireAdmin, uploadContentFiles, offerValidation, createOffer);

// @route   PUT api/content/offers/:id
// @desc    Update offer
// @access  Private (Admin only)
router.put('/offers/:id', authenticateToken, requireAdmin, uploadContentFiles, offerValidation, updateOffer);

// @route   DELETE api/content/offers/:id
// @desc    Delete offer
// @access  Private (Admin only)
router.delete('/offers/:id', authenticateToken, requireAdmin, deleteOffer);

// @route   PATCH api/content/offers/:id/toggle-status
// @desc    Toggle offer active status
// @access  Private (Admin only)
router.patch('/offers/:id/toggle-status', authenticateToken, requireAdmin, toggleOfferStatus);

// @route   POST api/content/offers/:id/view
// @desc    Increment offer view count
// @access  Public
router.post('/offers/:id/view', incrementOfferView);

// @route   POST api/content/offers/:id/click
// @desc    Increment offer click count
// @access  Public
router.post('/offers/:id/click', incrementOfferClick);

// =============================================================================
// PRODUCT ROUTES
// =============================================================================

// @route   GET api/content/products
// @desc    Get all products
// @access  Public for active products, Admin for all products
router.get('/products', async (req, res, next) => {
  // Optional authentication - check if token is provided and valid
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];

  let isAdmin = false;
  if (token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.id);
      if (user && user.role === 'admin') {
        req.user = user;
        isAdmin = true;
      }
    } catch (error) {
      // Invalid token, continue as non-admin
      console.log('Invalid token provided, continuing as non-admin');
    }
  }

  req.adminAccess = isAdmin;
  await getAllProducts(req, res, next);
});

// @route   GET api/content/products/search
// @desc    Search products
// @access  Public
router.get('/products/search', searchProducts);

// @route   GET api/content/products/featured
// @desc    Get featured products
// @access  Public
router.get('/products/featured', getFeaturedProducts);

// @route   GET api/content/products/category/:category
// @desc    Get products by category
// @access  Public
router.get('/products/category/:category', getProductsByCategory);

// @route   GET api/content/products/:id
// @desc    Get single product by ID
// @access  Public
router.get('/products/:id', getProductById);

// @route   POST api/content/products
// @desc    Create new product
// @access  Private (Admin only)
router.post('/products', authenticateToken, requireAdmin, uploadContentFiles, productValidation, createProduct);

// @route   PUT api/content/products/:id
// @desc    Update product
// @access  Private (Admin only)
router.put('/products/:id', authenticateToken, requireAdmin, uploadContentFiles, productValidation, updateProduct);

// @route   DELETE api/content/products/:id
// @desc    Delete product
// @access  Private (Admin only)
router.delete('/products/:id', authenticateToken, requireAdmin, deleteProduct);

// @route   PATCH api/content/products/:id/toggle-status
// @desc    Toggle product active status
// @access  Private (Admin only)
router.patch('/products/:id/toggle-status', authenticateToken, requireAdmin, toggleProductStatus);

// @route   POST api/content/products/:id/view
// @desc    Increment product view count
// @access  Public
router.post('/products/:id/view', incrementProductView);

export default router;

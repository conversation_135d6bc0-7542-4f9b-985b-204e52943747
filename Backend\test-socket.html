<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket.IO Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 Socket.IO Real-time Points Test</h1>
        
        <div id="status" class="status disconnected">
            ❌ Disconnected
        </div>
        
        <div>
            <h3>Authentication</h3>
            <input type="text" id="tokenInput" placeholder="Enter JWT token" style="width: 300px;">
            <button onclick="authenticate()">Authenticate</button>
        </div>
        
        <div>
            <h3>Actions</h3>
            <button onclick="connect()">Connect</button>
            <button onclick="disconnect()">Disconnect</button>
            <button onclick="ping()">Ping</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div>
            <h3>Connection Log</h3>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket = null;
        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logEl.innerHTML += `[${timestamp}] ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(message);
        }
        
        function updateStatus(connected) {
            if (connected) {
                statusEl.className = 'status connected';
                statusEl.innerHTML = '✅ Connected';
            } else {
                statusEl.className = 'status disconnected';
                statusEl.innerHTML = '❌ Disconnected';
            }
        }
        
        function connect() {
            if (socket && socket.connected) {
                log('Already connected');
                return;
            }
            
            log('Connecting to Socket.IO server...');
            socket = io('http://localhost:5000', {
                transports: ['websocket', 'polling']
            });
            
            socket.on('connect', () => {
                log(`✅ Connected with ID: ${socket.id}`);
                updateStatus(true);
            });
            
            socket.on('disconnect', (reason) => {
                log(`❌ Disconnected: ${reason}`);
                updateStatus(false);
            });
            
            socket.on('connect_error', (error) => {
                log(`❌ Connection error: ${error.message}`);
                updateStatus(false);
            });
            
            socket.on('authenticated', (data) => {
                log(`🔐 Authenticated: ${JSON.stringify(data)}`);
            });
            
            socket.on('auth_error', (error) => {
                log(`❌ Authentication error: ${JSON.stringify(error)}`);
            });
            
            socket.on('points_updated', (data) => {
                log(`📊 Points updated: Monthly=${data.monthlyPoints}, Yearly=${data.yearlyPoints}`);
            });
            
            socket.on('new_notification', (data) => {
                log(`🔔 New notification: ${JSON.stringify(data)}`);
            });
            
            socket.on('pong', (data) => {
                log(`🏓 Pong received: ${JSON.stringify(data)}`);
            });
        }
        
        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
                log('Disconnected from server');
                updateStatus(false);
            }
        }
        
        function authenticate() {
            const token = document.getElementById('tokenInput').value.trim();
            if (!token) {
                alert('Please enter a JWT token');
                return;
            }
            
            if (!socket || !socket.connected) {
                alert('Please connect first');
                return;
            }
            
            log(`🔐 Authenticating with token: ${token.substring(0, 20)}...`);
            socket.emit('authenticate', token);
        }
        
        function ping() {
            if (!socket || !socket.connected) {
                alert('Please connect first');
                return;
            }
            
            log('🏓 Sending ping...');
            socket.emit('ping');
        }
        
        function clearLog() {
            logEl.innerHTML = '';
        }
        
        // Auto-connect on page load
        window.onload = () => {
            log('Page loaded. Click Connect to start Socket.IO connection.');
        };
    </script>
</body>
</html>

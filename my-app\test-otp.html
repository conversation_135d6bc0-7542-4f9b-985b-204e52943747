<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTP Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 OTP Test Tool</h1>
        <p>Test OTP sending functionality with different configurations</p>
        
        <div class="form-group">
            <label for="phoneNumber">Phone Number (without +91):</label>
            <input type="text" id="phoneNumber" value="9399567171" placeholder="Enter 10-digit mobile number">
        </div>
        
        <div class="form-group">
            <label for="senderID">Sender ID:</label>
            <select id="senderID">
                <option value="FASTAGCAB">FASTAGCAB</option>
                <option value="InfoSMS">InfoSMS</option>
                <option value="TEST">TEST</option>
                <option value="VERIFY">VERIFY</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="messageTemplate">Message Template:</label>
            <select id="messageTemplate">
                <option value="full">Full Message (with company name)</option>
                <option value="simple">Simple OTP Message</option>
                <option value="basic">Basic Test Message</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="customMessage">Custom Message (optional):</label>
            <textarea id="customMessage" rows="3" placeholder="Leave empty to use template"></textarea>
        </div>
        
        <button onclick="sendOTP()">📱 Send OTP</button>
        <button onclick="clearLog()">🗑️ Clear Log</button>
        
        <div id="log" class="log">Ready to send OTP...\n</div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = 'Log cleared...\n';
        }

        function generateOTP() {
            return Math.floor(100000 + Math.random() * 900000).toString();
        }

        function getMessageTemplate(template, otp) {
            switch(template) {
                case 'full':
                    return `Your FASTAGCAB verification code is ${otp}. Valid for 15 minutes. Do not share this code with anyone.`;
                case 'simple':
                    return `Your verification code is ${otp}. Valid for 15 minutes.`;
                case 'basic':
                    return `Test OTP: ${otp}`;
                default:
                    return `Your verification code is ${otp}`;
            }
        }

        async function sendOTP() {
            const phoneNumber = document.getElementById('phoneNumber').value.trim();
            const senderID = document.getElementById('senderID').value;
            const messageTemplate = document.getElementById('messageTemplate').value;
            const customMessage = document.getElementById('customMessage').value.trim();
            
            if (!phoneNumber || phoneNumber.length !== 10) {
                log('❌ Please enter a valid 10-digit phone number', 'error');
                return;
            }
            
            const otp = generateOTP();
            const message = customMessage || getMessageTemplate(messageTemplate, otp);
            const formattedPhone = `+91${phoneNumber}`;
            
            log('🚀 Starting OTP send test...');
            log(`📱 Phone: ${formattedPhone}`);
            log(`🔢 OTP: ${otp}`);
            log(`📤 Sender: ${senderID}`);
            log(`💬 Message: ${message}`);
            
            const smsData = {
                messages: [
                    {
                        from: senderID,
                        destinations: [
                            {
                                to: formattedPhone
                            }
                        ],
                        text: message
                    }
                ]
            };
            
            log('📦 SMS Data: ' + JSON.stringify(smsData, null, 2));
            
            try {
                log('🌐 Sending request to Infobip...');
                
                const response = await fetch('https://ypqwxp.api.infobip.com/sms/2/text/advanced', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'App 5830f3c890eba242e7bf4e33e4dec772-f871e7d3-5cd4-48bc-ba6d-d590ff3f541e',
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(smsData)
                });
                
                log(`📡 Response Status: ${response.status}`);
                
                const responseData = await response.json();
                log('📋 Response Data: ' + JSON.stringify(responseData, null, 2));
                
                if (response.ok && responseData.messages && responseData.messages[0]) {
                    const msg = responseData.messages[0];
                    log('✅ SMS sent successfully!', 'success');
                    log(`🆔 Message ID: ${msg.messageId}`, 'success');
                    log(`📊 Status: ${msg.status?.name}`, 'success');
                    log(`📝 Description: ${msg.status?.description}`, 'success');
                    log('📱 Check your phone for the SMS (may take 1-5 minutes)', 'success');
                } else {
                    log('❌ SMS failed to send', 'error');
                    log('🚫 Error details: ' + JSON.stringify(responseData, null, 2), 'error');
                }
                
            } catch (error) {
                log('💥 Network error: ' + error.message, 'error');
                console.error('Full error:', error);
            }
        }
        
        // Auto-focus phone number input
        document.getElementById('phoneNumber').focus();
    </script>
</body>
</html>

{"expo": {"name": "FASTAGCAB", "slug": "fastagcab", "owner": "ankit3028k", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": false, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["assets/images/**/*", "assets/fonts/**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.ankit3028k.fastagcab", "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to let you take photos for your profile and documents.", "NSPhotoLibraryUsageDescription": "This app accesses your photo library to let you upload profile pictures and documents."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.ankit3028k.fastagcab", "edgeToEdgeEnabled": true, "permissions": ["INTERNET", "ACCESS_NETWORK_STATE", "CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE"], "versionCode": 1, "enableProguardInReleaseBuilds": true, "enableSeparateBuildPerCPUArchitecture": true}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-localization", ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you upload profile pictures and documents.", "cameraPermission": "The app accesses your camera to let you take photos for your profile and documents."}]], "experiments": {"typedRoutes": true}, "extra": {"eas": {"projectId": "9db60853-c3c9-4104-b92f-34ef153a1238"}}}}
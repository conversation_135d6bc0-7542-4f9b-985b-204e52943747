import PointHistory from '../Models/PointHistory.js';
import User from '../Models/user.js';
import { validationResult } from 'express-validator';

// @desc    Get user's point history
// @route   GET api/points/history
// @access  Private
export const getUserPointHistory = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      page = 1,
      limit = 20,
      transactionType,
      source,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const options = {
      page: parseInt(page, 10),
      limit: parseInt(limit, 10),
      transactionType,
      source,
      startDate,
      endDate,
      sortBy,
      sortOrder: sortOrder === 'desc' ? -1 : 1
    };

    const history = await PointHistory.getUserHistory(userId, options);

    const query = { userId };
    if (transactionType) query.transactionType = transactionType;
    if (source) query.source = source;
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    const totalCount = await PointHistory.countDocuments(query);
    const totalPages = Math.ceil(totalCount / options.limit);

    res.status(200).json({
      success: true,
      data: {
        history,
        pagination: {
          currentPage: options.page,
          totalPages,
          totalCount,
          hasNext: options.page < totalPages,
          hasPrev: options.page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get point history error:', error.message, error.stack);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching point history',
      error: error.message
    });
  }
};

// @desc    Get user's point statistics
// @route   GET api/points/stats
// @access  Private
export const getUserPointStats = async (req, res) => {
  try {
    const userId = req.user.id;
    const { period = '30d' } = req.query;

    const stats = await PointHistory.getUserStats(userId, period);

    // Get current points balance from latest PointHistory entry
    const latestEntry = await PointHistory.findOne({ userId, status: 'completed' })
      .sort({ createdAt: -1 })
      .select('pointsBalance metadata.monthlyPointsAfter metadata.yearlyPointsAfter metadata.lastMonthlyReset metadata.nextMonthlyReset metadata.nextYearlyReset metadata.registrationAnniversary');

    const pointBalances = {
      monthlyPoints: latestEntry?.metadata?.monthlyPointsAfter || 0,
      yearlyPoints: latestEntry?.metadata?.yearlyPointsAfter || 0,
      totalPoints: latestEntry?.pointsBalance || 0,
      lastMonthlyReset: latestEntry?.metadata?.lastMonthlyReset || null,
      nextMonthlyReset: latestEntry?.metadata?.nextMonthlyReset || null,
      nextYearlyReset: latestEntry?.metadata?.nextYearlyReset || null,
      registrationAnniversary: latestEntry?.metadata?.registrationAnniversary || null
    };

    const formattedStats = {
      currentPoints: {
        monthly: pointBalances.monthlyPoints,
        yearly: pointBalances.yearlyPoints,
        total: pointBalances.totalPoints
      },
      pointsInfo: {
        lastMonthlyReset: pointBalances.lastMonthlyReset,
        nextMonthlyReset: pointBalances.nextMonthlyReset,
        nextYearlyReset: pointBalances.nextYearlyReset,
        registrationAnniversary: pointBalances.registrationAnniversary
      },
      periodStats: stats.reduce((acc, stat) => {
        acc[stat._id] = {
          totalPoints: stat.totalPoints,
          count: stat.count,
          avgPoints: Math.round(stat.avgPoints * 100) / 100
        };
        return acc;
      }, {}),
      period
    };

    res.status(200).json({
      success: true,
      data: formattedStats
    });
  } catch (error) {
    console.error('Get point stats error:', error.message, error.stack);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching point statistics',
      error: error.message
    });
  }
};

// @desc    Get all users' point history (Admin only)
// @route   GET api/points/history/all
// @access  Private (Admin only)
export const getAllUsersPointHistory = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      userId,
      transactionType,
      source,
      startDate,
      endDate,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const query = {};
    if (userId) query.userId = userId;
    if (transactionType) query.transactionType = transactionType;
    if (source) query.source = source;
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) query.createdAt.$gte = new Date(startDate);
      if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    const history = await PointHistory.find(query)
      .populate('userId', 'fullName phoneNumber dealerCode role')
      .populate('sourceId')
      .populate('metadata.adminId', 'fullName')
      .sort({ [sortBy]: sortOrder === 'desc' ? -1 : 1 })
      .limit(parseInt(limit, 10))
      .skip((parseInt(page, 10) - 1) * parseInt(limit, 10));

    const totalCount = await PointHistory.countDocuments(query);
    const totalPages = Math.ceil(totalCount / parseInt(limit, 10));

    res.status(200).json({
      success: true,
      data: {
        history,
        pagination: {
          currentPage: parseInt(page, 10),
          totalPages,
          totalCount,
          hasNext: parseInt(page, 10) < totalPages,
          hasPrev: parseInt(page, 10) > 1
        }
      }
    });
  } catch (error) {
    console.error('Get all point history error:', error.message, error.stack);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching point history',
      error: error.message
    });
  }
};

// @desc    Create manual point adjustment (Admin only)
// @route   POST api/points/adjust
// @access  Private (Admin only)
export const adjustUserPoints = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId, pointsChange, reason, adjustmentType } = req.body;
    const adminId = req.user.id;

    // Find the user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get current points from latest PointHistory entry
    const latestEntry = await PointHistory.findOne({ userId, status: 'completed' })
      .sort({ createdAt: -1 })
      .select('pointsBalance metadata.monthlyPointsAfter metadata.yearlyPointsAfter');

    const currentPointsBalance = latestEntry?.pointsBalance || 0;
    const currentMonthlyPoints = latestEntry?.metadata?.monthlyPointsAfter || 0;
    const currentYearlyPoints = latestEntry?.metadata?.yearlyPointsAfter || 0;

    // Validate sufficient points for deduction
    if (pointsChange < 0) {
      const targetPoints = adjustmentType === 'yearly' ? currentYearlyPoints : currentMonthlyPoints;
      if (targetPoints + pointsChange < 0) {
        return res.status(400).json({
          success: false,
          message: `Insufficient ${adjustmentType} points. You have ${targetPoints} ${adjustmentType} points but need ${Math.abs(pointsChange)} points.`
        });
      }
      if (currentPointsBalance + pointsChange < 0) {
        return res.status(400).json({
          success: false,
          message: `Insufficient total points. You have ${currentPointsBalance} points but need ${Math.abs(pointsChange)} points.`
        });
      }
    }

    // Calculate new balances
    const newPointsBalance = Math.max(0, currentPointsBalance + pointsChange);
    const newMonthlyPoints = adjustmentType === 'monthly' ? Math.max(0, currentMonthlyPoints + pointsChange) : currentMonthlyPoints;
    const newYearlyPoints = adjustmentType === 'yearly' ? Math.max(0, currentYearlyPoints + pointsChange) : currentYearlyPoints;

    // Update user points in User model (if used)
    if (adjustmentType === 'yearly') {
      user.yearlyPoints = newYearlyPoints;
    } else {
      user.monthlyPoints = newMonthlyPoints;
    }
    await user.save();

    // Create point history entry
    const pointHistoryEntry = await PointHistory.createEntry({
      userId,
      transactionType: pointsChange > 0 ? 'earned' : 'adjusted',
      pointsChange,
      pointsBalance: newPointsBalance,
      source: 'admin_adjustment',
      description: reason,
      metadata: {
        adminId,
        adminNote: reason,
        adjustmentType,
        monthlyPointsBefore: currentMonthlyPoints,
        monthlyPointsAfter: newMonthlyPoints,
        yearlyPointsBefore: currentYearlyPoints,
        yearlyPointsAfter: newYearlyPoints,
        pointType: adjustmentType
      }
    });

    res.status(200).json({
      success: true,
      message: `Successfully ${pointsChange > 0 ? 'added' : 'deducted'} ${Math.abs(pointsChange)} ${adjustmentType} points`,
      data: {
        userId,
        pointsChange,
        newBalance: newPointsBalance,
        monthlyPoints: newMonthlyPoints,
        yearlyPoints: newYearlyPoints,
        adjustmentType
      }
    });
  } catch (error) {
    console.error('Adjust points error:', error.message, error.stack);
    res.status(500).json({
      success: false,
      message: 'Server error while adjusting points',
      error: error.message
    });
  }
};

// @desc    Get point history analytics (Admin only)
// @route   GET api/points/analytics
// @access  Private (Admin only)
export const getPointHistoryAnalytics = async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    const startDate = new Date();
    switch (period) {
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(startDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    // Transaction type distribution
    const transactionStats = await PointHistory.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          status: 'completed'
        }
      },
      {
        $group: {
          _id: '$transactionType',
          totalPoints: { $sum: '$pointsChange' },
          count: { $sum: 1 },
          avgPoints: { $avg: '$pointsChange' }
        }
      }
    ]);

    // Source distribution
    const sourceStats = await PointHistory.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          status: 'completed'
        }
      },
      {
        $group: {
          _id: '$source',
          totalPoints: { $sum: '$pointsChange' },
          count: { $sum: 1 }
        }
      }
    ]);

    // Daily trends with monthly/yearly breakdown
    const dailyTrends = await PointHistory.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          status: 'completed'
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          totalPoints: { $sum: '$pointsChange' },
          monthlyPoints: {
            $sum: {
              $cond: [{ $eq: ['$metadata.pointType', 'monthly'] }, '$pointsChange', { $cond: [{ $eq: ['$metadata.pointType', 'both'] }, '$pointsChange', 0] }]
            }
          },
          yearlyPoints: {
            $sum: {
              $cond: [{ $eq: ['$metadata.pointType', 'yearly'] }, '$pointsChange', { $cond: [{ $eq: ['$metadata.pointType', 'both'] }, '$pointsChange', 0] }]
            }
          },
          earnedPoints: {
            $sum: {
              $cond: [{ $gt: ['$pointsChange', 0] }, '$pointsChange', 0]
            }
          },
          redeemedPoints: {
            $sum: {
              $cond: [{ $lt: ['$pointsChange', 0] }, { $abs: '$pointsChange' }, 0]
            }
          },
          transactionCount: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        transactionStats,
        sourceStats,
        dailyTrends: dailyTrends.map(trend => ({
          date: `${trend._id.year}-${trend._id.month.toString().padStart(2, '0')}-${trend._id.day.toString().padStart(2, '0')}`,
          totalPoints: trend.totalPoints,
          monthlyPoints: trend.monthlyPoints,
          yearlyPoints: trend.yearlyPoints,
          earnedPoints: trend.earnedPoints,
          redeemedPoints: trend.redeemedPoints,
          transactionCount: trend.transactionCount
        })),
        period
      }
    });
  } catch (error) {
    console.error('Get point analytics error:', error.message, error.stack);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching point analytics',
      error: error.message
    });
  }
};
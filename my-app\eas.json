{"cli": {"version": ">= 5.9.1", "appVersionSource": "remote"}, "build": {"development": {"developmentClient": true, "distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "env": {"GRADLE_OPTS": "-Dorg.gradle.daemon=false -Dorg.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=512m"}}, "env": {"NODE_ENV": "production", "NODE_OPTIONS": "--max-old-space-size=4096", "EAS_SKIP_AUTO_FINGERPRINT": "1"}}, "preview-optimized": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "env": {"EXPO_OPTIMIZE_SIZE": "1"}}, "env": {"NODE_ENV": "production"}}, "production": {"autoIncrement": true, "android": {"buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}}, "debug-build": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "env": {"GRADLE_OPTS": "-Dorg.gradle.daemon=false -Dorg.gradle.debug=true -Dorg.gradle.jvmargs=-Xmx4g"}}, "env": {"EAS_SKIP_AUTO_FINGERPRINT": "1"}}, "fast-apk": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug", "env": {"GRADLE_OPTS": "-Dorg.gradle.daemon=false -Dorg.gradle.jvmargs=-Xmx2g"}}, "env": {"EAS_SKIP_AUTO_FINGERPRINT": "1", "EXPO_SKIP_MANIFEST_VALIDATION_TOKEN": "1", "NODE_ENV": "development", "EXPO_NO_CAPABILITY_SYNC": "1"}}, "bundle-fix": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease", "env": {"GRADLE_OPTS": "-Dorg.gradle.daemon=false -Dorg.gradle.jvmargs=-Xmx6g -XX:MaxMetaspaceSize=1g"}}, "env": {"NODE_ENV": "production", "NODE_OPTIONS": "--max-old-space-size=8192", "EAS_SKIP_AUTO_FINGERPRINT": "1", "EXPO_SKIP_MANIFEST_VALIDATION_TOKEN": "1", "EXPO_NO_CAPABILITY_SYNC": "1"}}}, "submit": {"production": {}}}
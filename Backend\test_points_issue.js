import mongoose from 'mongoose';
import User from './Models/user.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const testPointsIssue = async () => {
  try {
    console.log('🔄 Testing points issue...');
    
    // Connect to database
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URI || 'mongodb+srv://ankitgangrade9617:<EMAIL>/electrician-app');
    console.log('✅ Connected to MongoDB');
    
    // Find a test user (you can replace with actual phone number)
    const testPhoneNumber = '8959305284'; // Replace with actual test user phone
    
    console.log(`🔍 Looking for user with phone: ${testPhoneNumber}`);
    const user = await User.findOne({ phoneNumber: testPhoneNumber });
    
    if (!user) {
      console.log('❌ Test user not found');
      
      // List all users to see what's available
      const allUsers = await User.find().select('fullName phoneNumber monthlyPoints yearlyPoints').limit(5);
      console.log('📋 Available users:', allUsers);
      
      return;
    }
    
    console.log('✅ User found:', {
      id: user._id,
      fullName: user.fullName,
      phoneNumber: user.phoneNumber,
      monthlyPoints: user.monthlyPoints,
      yearlyPoints: user.yearlyPoints,
      role: user.role,
      status: user.status
    });
    
    // Test different query methods
    console.log('\n🧪 Testing different query methods:');
    
    // Method 1: findById
    const userById = await User.findById(user._id);
    console.log('1. findById result:', {
      monthlyPoints: userById.monthlyPoints,
      yearlyPoints: userById.yearlyPoints
    });
    
    // Method 2: findById with select
    const userByIdSelect = await User.findById(user._id).select('-password');
    console.log('2. findById with select result:', {
      monthlyPoints: userByIdSelect.monthlyPoints,
      yearlyPoints: userByIdSelect.yearlyPoints
    });
    
    // Method 3: findOne
    const userByPhone = await User.findOne({ phoneNumber: testPhoneNumber });
    console.log('3. findOne result:', {
      monthlyPoints: userByPhone.monthlyPoints,
      yearlyPoints: userByPhone.yearlyPoints
    });
    
    // Test updating points
    console.log('\n🔄 Testing points update...');
    const originalMonthly = user.monthlyPoints || 0;
    const originalYearly = user.yearlyPoints || 0;
    
    // Add some test points
    user.monthlyPoints = originalMonthly + 10;
    user.yearlyPoints = originalYearly + 10;
    await user.save();
    
    console.log('✅ Points updated successfully');
    
    // Verify the update
    const updatedUser = await User.findById(user._id);
    console.log('📊 Updated points:', {
      monthlyPoints: updatedUser.monthlyPoints,
      yearlyPoints: updatedUser.yearlyPoints
    });
    
    // Restore original points
    user.monthlyPoints = originalMonthly;
    user.yearlyPoints = originalYearly;
    await user.save();
    
    console.log('🔄 Points restored to original values');
    
    console.log('\n✅ Points issue test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('❌ Error stack:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('📡 Disconnected from MongoDB');
    process.exit(0);
  }
};

// Run the test
testPointsIssue();

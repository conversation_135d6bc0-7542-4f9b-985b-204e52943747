import React, { useState, useEffect } from 'react'
import { X, QrCode, Calendar, Package, Award, Search, Filter } from 'lucide-react'
import Card, { CardContent } from '../ui/Card'
import LoadingSpinner from '../ui/LoadingSpinner'
import toast from 'react-hot-toast'
import { qrAPI } from '../../lib/api'

interface ScannedQRCode {
  _id: string
  qrCode: string
  productName: string
  productSize: string
  points: number
  value: number
  status: 'Not Redeem' | 'Redeemed'
  redeemedAt: string
  createdAt: string
}

interface UserQRCodesModalProps {
  isOpen: boolean
  onClose: () => void
  userId: string
  userName: string
}

export default function UserQRCodesModal({ 
  isOpen, 
  onClose, 
  userId, 
  userName 
}: UserQRCodesModalProps) {
  const [qrCodes, setQrCodes] = useState<ScannedQRCode[]>([])
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedFilter, setSelectedFilter] = useState('all')

  const fetchUserQRCodes = async (page = 1, search = '', filter = 'all') => {
    if (!userId) return

    try {
      setLoading(true)

      // Try the new endpoint first
      try {
        const params = {
          page: page.toString(),
          limit: '10',
          sortOrder: 'desc'
        }

        if (search) {
          params.search = search
        }

        if (filter !== 'all') {
          params.productSize = filter
        }

        const data = await qrAPI.getUserScannedQRCodes(userId, params)

        if (data.success) {
          setQrCodes(data.data.qrCodes)
          setCurrentPage(data.data.pagination.currentPage)
          setTotalPages(data.data.pagination.totalPages)
          setTotalCount(data.data.pagination.totalCount)
          return
        }
      } catch (endpointError) {
        console.log('New endpoint not available, falling back to general QR endpoint:', endpointError.message)
      }

      // Fallback: Use general QR codes endpoint and filter by user
      const data = await qrAPI.getAll({
        page: 1,
        limit: 1000, // Get more records to filter
        status: 'Redeemed'
      })

      if (data.qrCodes) {
        // Filter QR codes for this specific user
        let filteredQRCodes = data.qrCodes.filter(qr =>
          qr.redeemedBy && qr.redeemedBy.toString() === userId.toString()
        )

        // Apply search filter
        if (search) {
          filteredQRCodes = filteredQRCodes.filter(qr =>
            qr.productName.toLowerCase().includes(search.toLowerCase()) ||
            qr.qrCode.toLowerCase().includes(search.toLowerCase())
          )
        }

        // Apply product size filter
        if (filter !== 'all') {
          filteredQRCodes = filteredQRCodes.filter(qr => qr.productSize === filter)
        }

        // Apply pagination
        const startIndex = (page - 1) * 10
        const endIndex = startIndex + 10
        const paginatedQRCodes = filteredQRCodes.slice(startIndex, endIndex)

        setQrCodes(paginatedQRCodes)
        setCurrentPage(page)
        setTotalPages(Math.ceil(filteredQRCodes.length / 10))
        setTotalCount(filteredQRCodes.length)
      } else {
        toast.error('Failed to fetch QR codes')
      }
    } catch (error) {
      console.error('Fetch user QR codes error:', error)
      toast.error('Failed to fetch QR codes')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isOpen && userId) {
      fetchUserQRCodes(1, searchTerm, selectedFilter)
    }
  }, [isOpen, userId])

  const handleSearch = () => {
    setCurrentPage(1)
    fetchUserQRCodes(1, searchTerm, selectedFilter)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchUserQRCodes(page, searchTerm, selectedFilter)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getProductSizeColor = (size: string) => {
    const colors = {
      '0.75 mm': { text: '#1ca63a', bg: '#1ca63a15' },
      '1.0 mm': { text: '#df5921', bg: '#df592115' },
      '1.5 mm': { text: '#d5a81a', bg: '#d5a81a15' },
      '2.5 mm': { text: '#7e8689', bg: '#7e868915' }
    }
    return colors[size as keyof typeof colors] || { text: '#7e8689', bg: '#7e868915' }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-0 border w-full max-w-5xl shadow-lg rounded-lg bg-white mb-4">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b" style={{ borderColor: '#7e8689' }}>
          <div>
            <h2 className="text-xl font-bold" style={{ color: '#1A1A1A' }}>
              Scanned QR Codes - {userName}
            </h2>
            <p style={{ color: '#7e8689' }}>View all QR codes scanned by this user</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            style={{ color: '#7e8689' }}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Search and Filters */}
        <div className="p-6 border-b" style={{ borderColor: '#7e8689' }}>
          <div className="flex items-center gap-4 flex-wrap">
            <div className="flex items-center gap-2 flex-1 min-w-64">
              <Search className="h-5 w-5" style={{ color: '#7e8689' }} />
              <input
                type="text"
                placeholder="Search by product name or QR code..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="flex-1 border rounded-md px-3 py-2 focus:outline-none focus:ring-2"
                style={{ 
                  borderColor: '#7e8689',
                  color: '#1A1A1A'
                }}
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5" style={{ color: '#7e8689' }} />
              <select
                value={selectedFilter}
                onChange={(e) => setSelectedFilter(e.target.value)}
                className="border rounded-md px-3 py-2 focus:outline-none focus:ring-2"
                style={{ 
                  borderColor: '#7e8689',
                  color: '#1A1A1A'
                }}
              >
                <option value="all">All Sizes</option>
                <option value="0.75 mm">0.75 mm</option>
                <option value="1.0 mm">1.0 mm</option>
                <option value="1.5 mm">1.5 mm</option>
                <option value="2.5 mm">2.5 mm</option>
              </select>
            </div>
            <button
              onClick={handleSearch}
              className="px-4 py-2 rounded-md text-white font-medium hover:opacity-90 transition-opacity"
              style={{ backgroundColor: '#1ca63a' }}
            >
              Search
            </button>
            <span style={{ color: '#7e8689' }}>
              Total: {totalCount} QR codes
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="p-6" style={{ maxHeight: '60vh', overflowY: 'auto' }}>
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <LoadingSpinner />
            </div>
          ) : qrCodes.length === 0 ? (
            <div className="text-center py-8">
              <QrCode className="h-16 w-16 mx-auto mb-4" style={{ color: '#7e8689' }} />
              <p style={{ color: '#7e8689' }}>No QR codes found for this user.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {qrCodes.map((qr) => {
                const sizeColors = getProductSizeColor(qr.productSize)
                return (
                  <Card key={qr._id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        {/* Header */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <QrCode className="h-5 w-5" style={{ color: '#1ca63a' }} />
                            <span 
                              className="px-2 py-1 text-xs font-semibold rounded-full"
                              style={{ 
                                color: sizeColors.text,
                                backgroundColor: sizeColors.bg
                              }}
                            >
                              {qr.productSize}
                            </span>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center space-x-1">
                              <Award className="h-4 w-4" style={{ color: '#d5a81a' }} />
                              <span className="text-sm font-bold" style={{ color: '#d5a81a' }}>
                                {qr.points} pts
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Product Info */}
                        <div>
                          <div className="flex items-center space-x-2 mb-2">
                            <Package className="h-4 w-4" style={{ color: '#7e8689' }} />
                            <h3 className="font-medium" style={{ color: '#1A1A1A' }}>
                              {qr.productName}
                            </h3>
                          </div>
                          <p className="text-sm font-mono bg-gray-50 p-2 rounded border" style={{ color: '#7e8689' }}>
                            {qr.qrCode}
                          </p>
                        </div>

                        {/* Value */}
                        <div className="flex items-center justify-between">
                          <span className="text-sm" style={{ color: '#7e8689' }}>Value:</span>
                          <span className="font-medium" style={{ color: '#1A1A1A' }}>
                            ₹{qr.value}
                          </span>
                        </div>

                        {/* Scan Date */}
                        <div className="flex items-center space-x-2 pt-2 border-t" style={{ borderColor: '#7e8689' }}>
                          <Calendar className="h-4 w-4" style={{ color: '#7e8689' }} />
                          <div>
                            <p className="text-xs" style={{ color: '#7e8689' }}>Scanned on</p>
                            <p className="text-sm font-medium" style={{ color: '#1A1A1A' }}>
                              {formatDate(qr.redeemedAt)}
                            </p>
                          </div>
                        </div>

                        {/* Status Badge */}
                        <div className="flex justify-center pt-2">
                          <span 
                            className="px-3 py-1 text-xs font-semibold rounded-full"
                            style={{ 
                              color: '#ffffff',
                              backgroundColor: qr.status === 'Redeemed' ? '#1ca63a' : '#7e8689'
                            }}
                          >
                            {qr.status}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t" style={{ borderColor: '#7e8689' }}>
              <div className="text-sm" style={{ color: '#7e8689' }}>
                Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} results
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-3 py-2 text-sm font-medium border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ 
                    color: '#1A1A1A',
                    borderColor: '#7e8689'
                  }}
                >
                  Previous
                </button>
                <span className="px-3 py-2 text-sm" style={{ color: '#7e8689' }}>
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 text-sm font-medium border rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ 
                    color: '#1A1A1A',
                    borderColor: '#7e8689'
                  }}
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Summary Footer */}
        <div className="p-6 border-t bg-gray-50" style={{ borderColor: '#7e8689' }}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold" style={{ color: '#1ca63a' }}>
                {qrCodes.reduce((sum, qr) => sum + qr.points, 0)}
              </p>
              <p className="text-sm" style={{ color: '#7e8689' }}>Total Points Earned</p>
            </div>
            <div>
              <p className="text-2xl font-bold" style={{ color: '#df5921' }}>
                ₹{qrCodes.reduce((sum, qr) => sum + qr.value, 0)}
              </p>
              <p className="text-sm" style={{ color: '#7e8689' }}>Total Value Scanned</p>
            </div>
            <div>
              <p className="text-2xl font-bold" style={{ color: '#d5a81a' }}>
                {totalCount}
              </p>
              <p className="text-sm" style={{ color: '#7e8689' }}>QR Codes Scanned</p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t" style={{ borderColor: '#7e8689' }}>
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium rounded-md hover:opacity-90 transition-opacity"
            style={{ 
              backgroundColor: '#7e8689',
              color: '#ffffff'
            }}
          >
            Close
          </button>
        </div>
      </div>
    </div>
  )
}

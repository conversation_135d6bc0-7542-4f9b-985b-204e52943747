import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform, TextInput } from 'react-native';

// Conditionally import DateTimePicker only for mobile platforms
let DateTimePicker: any = null;
if (Platform.OS !== 'web') {
  DateTimePicker = require('@react-native-community/datetimepicker').default;
}

export default function DatePickerInput({
  date,
  onChange,
  error
}: {
  date: Date | null;
  onChange: (date: Date) => void;
  error?: string;
}) {
  const [show, setShow] = React.useState(false);

  // Helper function to safely convert date to string
  const dateToString = (dateObj: Date | null): string => {
    if (!dateObj || !(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
      return '';
    }
    return dateObj.toISOString().split('T')[0];
  };

  const [webDateValue, setWebDateValue] = React.useState(dateToString(date));

  // Update webDateValue when date prop changes
  React.useEffect(() => {
    if (Platform.OS === 'web') {
      setWebDateValue(dateToString(date));
    }
  }, [date]);

  const handleWebDateChange = (value: string) => {
    setWebDateValue(value);
    if (value) {
      const selectedDate = new Date(value);
      onChange(selectedDate);
    }
  };

  // Web-specific date input
  if (Platform.OS === 'web') {
    return (
      <View style={{ marginBottom: 16 }}>
        <Text style={styles.label}>Date of Birth *</Text>
        <TextInput
          style={[styles.input, error && styles.inputError]}
          value={webDateValue}
          onChangeText={handleWebDateChange}
          placeholder="YYYY-MM-DD"
          // Use HTML5 date input type on web
          {...(Platform.OS === 'web' && { type: 'date' })}
        />
        {error ? <Text style={styles.error}>{error}</Text> : null}
      </View>
    );
  }

  // Mobile-specific date picker
  return (
    <View style={{ marginBottom: 16 }}>
      <Text style={styles.label}>Date of Birth *</Text>
      <TouchableOpacity onPress={() => setShow(true)} style={[styles.input, error && styles.inputError]}>
        <Text>{date ? date.toLocaleDateString('en-GB') : 'DD/MM/YYYY'}</Text>
      </TouchableOpacity>
      {show && DateTimePicker && (
        <DateTimePicker
          value={date ?? new Date(2000, 0, 1)}
          mode="date"
          maximumDate={new Date()}
          minimumDate={new Date(1900, 0, 1)}
          onChange={(_: any, selected?: Date) => {
            setShow(Platform.OS === 'ios');
            selected && onChange(selected);
          }}
        />
      )}
      {error ? <Text style={styles.error}>{error}</Text> : null}
    </View>
  );
}

const styles = StyleSheet.create({
  label: { marginBottom: 4, fontWeight: '600' },
  input: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 10,
    backgroundColor: '#fff',
  },
  inputError: { borderColor: 'red' },
  error: { color: 'red', marginTop: 4 },
});

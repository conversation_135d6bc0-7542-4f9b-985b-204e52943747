import Recharge from '../Models/Recharge.js';
import mongoose from 'mongoose';

/**
 * GET /api/recharges/admin
 * Fetch all recharge requests for admin
 */
export const getAllRechargesForAdmin = async (req, res) => {
  try {
    const recharges = await Recharge.find()
      .populate('userId', 'fullName phoneNumber')
      .sort({ rechargeDate: -1 });

    return res.status(200).json({
      success: true,
      data: {
        recharges
      }
    });
  } catch (error) {
    console.error('Error fetching recharges for admin:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * PATCH /api/recharges/admin/:id
 * Update recharge status (completed/denied)
 */
export const updateRechargeStatusByAdmin = async (req, res) => {
  const { id } = req.params;
  const { status, adminNotes, processedBy } = req.body;

  if (!['completed', 'denied'].includes(status)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid status update'
    });
  }

  try {
    const recharge = await Recharge.findById(id);

    if (!recharge) {
      return res.status(404).json({
        success: false,
        message: 'Recharge not found'
      });
    }

    if (recharge.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Only pending recharges can be updated'
      });
    }

    recharge.status = status;
    recharge.processedDate = new Date();
    recharge.processedBy = mongoose.Types.ObjectId(processedBy);
    recharge.adminNotes = adminNotes || '';

    await recharge.save();

    return res.status(200).json({
      success: true,
      message: `Recharge marked as ${status}`,
      data: recharge
    });
  } catch (error) {
    console.error('Error updating recharge status:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

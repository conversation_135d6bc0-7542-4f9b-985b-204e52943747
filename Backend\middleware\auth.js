import jwt from 'jsonwebtoken';
import User from '../Models/user.js';

// Middleware to verify JWT token
export const authenticateToken = async (req, res, next) => {
    try {
        console.log('🔍 Auth middleware called for:', req.method, req.path);
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            console.log('❌ No token provided');
            return res.status(401).json({
                success: false,
                message: 'Access token is required'
            });
        }

        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        console.log('🔍 Decoded token payload:', decoded);

        // Get user from token
        const user = await User.findById(decoded.id).select('-password');
        console.log('🔍 User found in middleware:', user ? {
            id: user._id,
            fullName: user.fullName,
            monthlyPoints: user.monthlyPoints,
            yearlyPoints: user.yearlyPoints
        } : 'No user found');

        if (!user) {
            return res.status(401).json({
                success: false,
                message: 'Invalid token - user not found'
            });
        }

        req.user = user;
        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                success: false,
                message: 'Invalid token'
            });
        }

        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                message: 'Token expired'
            });
        }

        return res.status(500).json({
            success: false,
            message: 'Server error during authentication'
        });
    }
};

// Middleware to check if user is admin
export const requireAdmin = (req, res, next) => {
    if (req.user && req.user.role === 'admin') {
        next();
    } else {
        return res.status(403).json({
            success: false,
            message: 'Admin access required'
        });
    }
};

// Middleware to check if user can access resource (own resource or admin)
export const requireOwnershipOrAdmin = (req, res, next) => {
    const userId = req.params.id;

    if (req.user.role === 'admin' || req.user._id.toString() === userId) {
        next();
    } else {
        return res.status(403).json({
            success: false,
            message: 'Access denied - insufficient permissions'
        });
    }
};
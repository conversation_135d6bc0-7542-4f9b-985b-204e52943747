import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import React, { useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Platform,
} from 'react-native';

interface PhotoUploadProps {
  title: string;
  subtitle?: string;
  value?: string;
  onPhotoSelected: (uri: string) => void;
  error?: string;
  required?: boolean;
  icon?: keyof typeof Ionicons.glyphMap;
  cameraOnly?: boolean; // New prop to restrict to camera only
}

export default function PhotoUpload({
  title,
  subtitle,
  value,
  onPhotoSelected,
  error,
  required = false,
  icon = 'camera-outline',
  cameraOnly = false
}: PhotoUploadProps) {
  const [isLoading, setIsLoading] = useState(false);



  const pickImage = async () => {
    // If cameraOnly is true, directly open camera
    if (cameraOnly) {
      openCamera();
      return;
    }

    const hasPermission = await requestMediaLibraryPermissions();
    if (!hasPermission) return;

    Alert.alert(
      'Select Photo',
      'Choose how you want to select a photo',
      [
        {
          text: 'Camera',
          onPress: openCamera,
        },
        {
          text: 'Gallery',
          onPress: openGallery,
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const openCamera = async () => {
    const hasPermission = await requestCameraPermissions();
    if (!hasPermission) return;

    setIsLoading(true);

    try {
      console.log('📸 Starting photo selection process...');

      Alert.alert(
        'Select Photo',
        'Choose how you want to select a photo',
        [
          {
            text: 'Gallery',
            onPress: () => {
              console.log('📸 User selected gallery');
              openGallerySimple();
            },
          },
          {
            text: 'Camera',
            onPress: () => {
              console.log('📸 User selected camera');
              openCameraSimple();
            },
          },
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => console.log('📸 User canceled photo selection'),
          },
        ]
      );
    } catch (error: any) {
      console.error('📸 Photo selection error:', error);
      Alert.alert('Error', `Failed to start photo selection: ${error.message}`);
    }
  };

  // Simplified gallery function
  const openGallerySimple = async () => {
    setIsLoading(true);
    try {
      console.log('🖼️ Opening gallery (simple)...');

      // Request permission
      const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      console.log('🖼️ Permission result:', permissionResult);

      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera roll is required!');
        return;
      }

      // Launch image library
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      console.log('🖼️ Gallery result:', result);

      if (!result.canceled) {
        const imageUri = result.assets[0].uri;
        console.log('🖼️ Selected image URI:', imageUri);
        onPhotoSelected(imageUri);
        Alert.alert('Success', 'Photo selected successfully!');
      }
    } catch (error: any) {
      console.error('🖼️ Gallery error:', error);
      Alert.alert('Error', `Gallery error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Simplified camera function
  const openCameraSimple = async () => {
    setIsLoading(true);
    try {
      console.log('📷 Opening camera (simple)...');

      // Request permission
      const permissionResult = await ImagePicker.requestCameraPermissionsAsync();
      console.log('📷 Permission result:', permissionResult);

      if (permissionResult.granted === false) {
        Alert.alert('Permission Required', 'Permission to access camera is required!');
        return;
      }

      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: true,
        aspect: [4, 3],
        quality: 1,
      });

      console.log('📷 Camera result:', result);

      if (!result.canceled) {
        const imageUri = result.assets[0].uri;
        console.log('📷 Captured image URI:', imageUri);
        onPhotoSelected(imageUri);
        Alert.alert('Success', 'Photo captured successfully!');
      }
    } catch (error: any) {
      console.error('📷 Camera error:', error);
      Alert.alert('Error', `Camera error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };



  const removePhoto = () => {
    Alert.alert(
      'Remove Photo',
      'Are you sure you want to remove this photo?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => onPhotoSelected(''),
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <View style={styles.labelContainer}>
        <Text style={styles.label}>
          {title}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
        {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      </View>

      <TouchableOpacity
        style={[styles.uploadContainer, error ? styles.uploadError : null]}
        onPress={value ? removePhoto : pickImage}
        disabled={isLoading}
      >
        {isLoading ? (
          <ActivityIndicator size="large" color="#1ca63a" />
        ) : value ? (
          <View style={styles.imageContainer}>
            <Image source={{ uri: value }} style={styles.image} />
            <View style={styles.imageOverlay}>
              <Ionicons name="trash-outline" size={24} color="#fff" />
              <Text style={styles.overlayText}>Tap to remove</Text>
            </View>
          </View>
        ) : (
          <View style={styles.placeholderContainer}>
            <Ionicons name={icon} size={40} color="#666" />
            <Text style={styles.placeholderText}>Tap to upload photo</Text>
            <Text style={styles.placeholderSubtext}>Camera or Gallery</Text>
          </View>
        )}
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  labelContainer: {
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  required: {
    color: '#df5921',
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  uploadContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#e1e5e9',
    borderStyle: 'dashed',
    minHeight: 140,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  uploadError: {
    borderColor: '#df5921',
  },
  placeholderContainer: {
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 16,
    color: '#666',
    marginTop: 8,
    fontWeight: '500',
  },
  placeholderSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 4,
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    height: 120,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  imageOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlayText: {
    color: '#fff',
    fontSize: 14,
    marginTop: 4,
    fontWeight: '500',
  },
  errorText: {
    color: '#df5921',
    fontSize: 14,
    marginTop: 4,
  },
});

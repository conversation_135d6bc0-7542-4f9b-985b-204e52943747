import mongoose from 'mongoose';

const giftRedemptionSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    index: true
  },
  productId: {
    type: String,
    required: [true, 'Product ID is required'],
    trim: true
  },
  productName: {
    type: String,
    required: [true, 'Product name is required'],
    trim: true,
    maxlength: [200, 'Product name cannot exceed 200 characters']
  },
  productImage: {
    type: String,
    required: [true, 'Product image is required'],
    trim: true
  },
  pointsRequired: {
    type: Number,
    required: [true, 'Points required is required'],
    min: [1, 'Points required must be at least 1']
  },
  pointsDeducted: {
    type: Number,
    default: 0,
    min: [0, 'Points deducted cannot be negative']
  },
  userYearlyPointsAtRedemption: {
    type: Number,
    required: [true, 'User yearly points at redemption is required'],
    min: [0, 'User yearly points cannot be negative']
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'denied', 'cancelled'],
    default: 'pending',
    index: true
  },
  redemptionDate: {
    type: Date,
    default: Date.now
  },
  processedDate: {
    type: Date,
    default: null
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  adminNotes: {
    type: String,
    trim: true,
    maxlength: [500, 'Admin notes cannot exceed 500 characters']
  },
  userNotificationSent: {
    type: Boolean,
    default: false
  },
  dashboardNotificationSent: {
    type: Boolean,
    default: false
  },
  userNotificationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Notification',
    default: null
  },
  dashboardNotificationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Notification',
    default: null
  },
  // Additional metadata
  metadata: {
    userAgent: String,
    ipAddress: String,
    deviceInfo: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
giftRedemptionSchema.index({ userId: 1, status: 1 });
giftRedemptionSchema.index({ status: 1, createdAt: -1 });
giftRedemptionSchema.index({ redemptionDate: -1 });

// Virtual for checking if redemption is still pending
giftRedemptionSchema.virtual('isPending').get(function() {
  return this.status === 'pending';
});

// Virtual for checking if redemption is processed
giftRedemptionSchema.virtual('isProcessed').get(function() {
  return ['approved', 'denied'].includes(this.status);
});

// Static method to get pending redemptions for dashboard
giftRedemptionSchema.statics.getPendingRedemptions = function(options = {}) {
  const {
    page = 1,
    limit = 20,
    sortBy = 'createdAt',
    sortOrder = -1
  } = options;

  return this.find({ status: 'pending' })
    .populate('userId', 'fullName phoneNumber dealerCode')
    .sort({ [sortBy]: sortOrder })
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .exec();
};

// Static method to get user redemption history
giftRedemptionSchema.statics.getUserRedemptions = function(userId, options = {}) {
  const {
    page = 1,
    limit = 10,
    status = null,
    sortBy = 'createdAt',
    sortOrder = -1
  } = options;

  const query = { userId };
  if (status) query.status = status;

  return this.find(query)
    .sort({ [sortBy]: sortOrder })
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .exec();
};

// Instance method to approve redemption
giftRedemptionSchema.methods.approve = function(processedBy, adminNotes = '') {
  this.status = 'approved';
  this.processedDate = new Date();
  this.processedBy = processedBy;
  this.adminNotes = adminNotes;
  this.pointsDeducted = this.pointsRequired;
  return this.save();
};

// Instance method to deny redemption
giftRedemptionSchema.methods.deny = function(processedBy, adminNotes = '') {
  this.status = 'denied';
  this.processedDate = new Date();
  this.processedBy = processedBy;
  this.adminNotes = adminNotes;
  return this.save();
};

// Instance method to cancel redemption
giftRedemptionSchema.methods.cancel = function() {
  this.status = 'cancelled';
  this.processedDate = new Date();
  return this.save();
};

// Pre-save middleware to validate points
giftRedemptionSchema.pre('save', function(next) {
  if (this.isNew && this.userYearlyPointsAtRedemption < this.pointsRequired) {
    return next(new Error('Insufficient points for redemption'));
  }
  next();
});

export default mongoose.models.GiftRedemption || mongoose.model('GiftRedemption', giftRedemptionSchema);

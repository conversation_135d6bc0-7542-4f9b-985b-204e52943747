import { LanguageSwitcher } from '../../components/LanguageSwitcher';
import { QRScanner } from '../../components/QRScanner';
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import socketService from '@/services/socketService';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  FlatList,
  Image,
  Modal,
  // RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import WhatsAppButton from '@/components/WhatsappIcon';
import getServerBaseUrl from '@/envConfig';
const BackendURL = getServerBaseUrl();

// Safe dimension access for React Native
const { width } = Dimensions.get('window');

// TypeScript Interfaces
interface Notification {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
}

interface Banner {
  _id: string;
  title: string;
  description?: string;
  imageUrl: string;
  actionUrl?: string;
  isActive: boolean;
}

interface Offer {
  _id: string;
  title: string;
  description: string;
  imageUrl: string;
  offerType: string;
  discountValue?: number;
  isActive: boolean;
}

interface Product {
  _id: string;
  name: string;
  description: string;
  shortDescription?: string;
  category: string;
  brand?: string;
  price: number;
  originalPrice?: number;
  images: Array<{
    url: string;
    alt?: string;
    isPrimary: boolean;
  }>;
  primaryImage?: string;
  isActive: boolean;
}

// Theme constants
const theme = {
  shadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  borderRadius: {
    card: 16,
    button: 12,
    image: 10,
  },
};




export default function HomeScreen() {
  const { user, processRecharge, refreshUserData } = useAuth();
  const { colors } = useTheme();
  const { openDrawer } = require('@/contexts/DrawerContext').useDrawer();
  const { t, i18n } = useTranslation();
  const router = useRouter();
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [showQRScanner, setShowQRScanner] = useState(false);
  const [showLanguageSwitcher, setShowLanguageSwitcher] = useState(false);
  const [showNotificationModal, setShowNotificationModal] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isLoadingNotifications, setIsLoadingNotifications] = useState(false);
  const [notificationError, setNotificationError] = useState<string | null>(null);
  const [banners, setBanners] = useState<Banner[]>([]);
  const [offers, setOffers] = useState<Offer[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoadingBanners, setIsLoadingBanners] = useState(false);
  const [isLoadingOffers, setIsLoadingOffers] = useState(false);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [isSocketConnected, setIsSocketConnected] = useState(false);

  const backendUrl = BackendURL;

  console.log("url is",process.env.EXPO_PUBLIC_API_URL);

  // Fallback banner images (used when API fails)
  const fallbackBannerImages = [
    'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=200&fit=crop&crop=center&auto=format&q=80',
    'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=200&fit=crop&crop=center&auto=format&q=80',
    'https://images.unsplash.com/photo-1556740758-90de374c12ad?w=400&h=200&fit=crop&crop=center&auto=format&q=80',
    'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=200&fit=crop&crop=center&auto=format&q=80',
  ];

  // Get banner images for display (from API or fallback)
  const bannerImages = banners.length > 0
    ? banners.map(banner => banner.imageUrl.startsWith('http') ? banner.imageUrl : `${backendUrl}${banner.imageUrl}`)
    : fallbackBannerImages;

  // Auto-sliding banner
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentBannerIndex((prevIndex) =>
        prevIndex === bannerImages.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000); // Increased to 5 seconds

    return () => clearInterval(interval);
  }, [bannerImages.length]);

  // Refresh user data on component mount to ensure fresh points data
  // useEffect(() => {
  //   const refreshDataOnMount = async () => {
  //     if (user && refreshUserData) {
  //       console.log('🔄 Refreshing user data on component mount...');
  //       try {
  //         const result = await refreshUserData();
  //         if (result.success) {
  //           console.log('✅ User data refreshed on mount');
  //         }
  //       } catch (error) {
  //         console.error('❌ Failed to refresh user data on mount:', error);
  //       }
  //     }
  //   };

  //   refreshDataOnMount();
  // }, [user?._id, refreshUserData]); // Only run when user ID changes or refreshUserData is available

  // Fetch banners
  useEffect(() => {
    const fetchBanners = async () => {
      setIsLoadingBanners(true);
      try {
        const response = await fetch(`${backendUrl}/api/content/banners?limit=10`);
        if (!response.ok) throw new Error('Failed to fetch banners');
        const data = await response.json();
        setBanners(data.data?.banners || []);
      } catch (error) {
        console.error('Failed to fetch banners:', error);
        setBanners([]); // Will use fallback images
      } finally {
        setIsLoadingBanners(false);
      }
    };
    fetchBanners();
  }, [backendUrl]);

  // Fetch offers
  useEffect(() => {
    const fetchOffers = async () => {
      setIsLoadingOffers(true);
      try {
        const response = await fetch(`${backendUrl}/api/content/offers?limit=10`);
        if (!response.ok) throw new Error('Failed to fetch offers');
        const data = await response.json();
        setOffers(data.data?.offers || []);
      } catch (error) {
        console.error('Failed to fetch offers:', error);
        setOffers([]);
      } finally {
        setIsLoadingOffers(false);
      }
    };
    fetchOffers();
  }, [backendUrl]);

  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      setIsLoadingProducts(true);
      try {
        const response = await fetch(`${backendUrl}/api/content/products?limit=20`);
        if (!response.ok) throw new Error('Failed to fetch products');
        const data = await response.json();
        setProducts(data.data?.products || []);
      } catch (error) {
        console.error('Failed to fetch products:', error);
        // Fallback products with proper structure
        setProducts([
          // ...fallback product objects...
        ] as Product[]);
      } finally {
        setIsLoadingProducts(false);
      }
    };
    fetchProducts();
  }, [backendUrl]);
  const handleStatusPress = useCallback((status: string) => {
    Alert.alert('Status', `Current status: ${status}`);
  }, []);

  const handleQRScan = useCallback(() => {
    setShowQRScanner(true);
  }, []);

  const handleRecharge = useCallback(() => {
    Alert.prompt(
      'Recharge',
      'Enter recharge amount (₹)',
      async (amount) => {
        if (amount && !isNaN(Number(amount))) {
          const result = await processRecharge?.(Number(amount));
          if (result) {
            Alert.alert(result.success ? 'Success' : 'Error', result.message);
          }
        }
      },
      'plain-text',
      '',
      'numeric'
    );
  }, [processRecharge]);

  const getStatusColor = useCallback(
    (status?: string) => {
      switch (status) {
        case 'approved':
          return colors.success;
        case 'pending':
          return colors.warning;
        case 'declined':
          return colors.error;
        default:
          return colors.textSecondary;
      }
    },
    [colors]
  );

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      console.log('🔄 Refreshing user data and content...');

      // Refresh user data including points
      if (refreshUserData) {
        const result = await refreshUserData();
        if (result.success) {
          console.log('✅ User data refreshed successfully');
        } else {
          console.log('⚠️ Failed to refresh user data:', result.message);
        }
      }

      // Refresh banners
      try {
        const bannersResponse = await fetch(`${backendUrl}/api/content/banners?limit=10`);
        if (bannersResponse.ok) {
          const bannersData = await bannersResponse.json();
          setBanners(bannersData.data?.banners || []);
        }
      } catch (error) {
        console.error('Failed to refresh banners:', error);
      }

      // Refresh offers
      try {
        const offersResponse = await fetch(`${backendUrl}/api/content/offers?limit=10`);
        if (offersResponse.ok) {
          const offersData = await offersResponse.json();
          setOffers(offersData.data?.offers || []);
        }
      } catch (error) {
        console.error('Failed to refresh offers:', error);
      }

      // Refresh products
      try {
        const productsResponse = await fetch(`${backendUrl}/api/content/products?limit=20`);
        if (productsResponse.ok) {
          const productsData = await productsResponse.json();
          setProducts(productsData.data?.products || []);
        }
      } catch (error) {
        console.error('Failed to refresh products:', error);
      }

    } catch (error) {
      console.error('❌ Error during refresh:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refreshUserData, backendUrl]);

  console.log("user is",user);

  // Socket connection status listener
  useEffect(() => {
    const unsubscribe = socketService.onConnectionChange((connected) => {
      setIsSocketConnected(connected);
    });

    // Set initial connection status
    setIsSocketConnected(socketService.isConnected());

    return () => {
      unsubscribe();
    };
  }, []);


  // const handleReferralShare = async () => {
  //   if (!user?.referralCode) {
  //     Alert.alert('Error', 'Referral code not available');
  //     return;
  //   }
  //   const referralLink = `https://myapp.com/ref/${user.referralCode}`;
  //   try {
  //     await Share.share({
  //       message: `Join me on this amazing app and earn 200 points! Use my referral code: ${user.referralCode}\n\n${referralLink}`,
  //       title: 'Join and Earn Points!',
  //     });
  //   } catch (error) {
  //     console.error('Error sharing:', error);
  //     Alert.alert('Error', 'Failed to share referral link');
  //   }
  // };




  const fetchNotifications = useCallback(async () => {
    setIsLoadingNotifications(true);
    setNotificationError(null);

    try {
      if (!backendUrl) throw new Error('API URL not configured');
      if (!(user as any)?.token) throw new Error('User not authenticated');

      const response = await fetch(`${backendUrl}/notifications`, {
        headers: {
          Authorization: `Bearer ${(user as any)?.token}`,
        },
      });

      if (!response.ok) throw new Error('Network response was not ok');
      

      const data = await response.json();
      setNotifications(data.notifications || []);
    } catch (error) {
      setNotificationError(t('notificationError'));
      console.error('Notification fetch error:', error);
    } finally {
      setIsLoadingNotifications(false);
    }
  }, [backendUrl, (user as any)?.token, t]);

  const handleNotificationPress = useCallback(async () => {
    setShowNotificationModal(true);
    await fetchNotifications();
  }, [fetchNotifications]);

  const markNotificationAsRead = useCallback((notificationId: string) => {
    setNotifications((prev) =>
      prev.map((notification) =>
        notification.id === notificationId ? { ...notification, read: true } : notification
      )
    );
  }, []);

  const getUnreadCount = useCallback(() => {
    return notifications.filter((notification) => !notification.read).length;
  }, [notifications]);

  const getStatusIcon = useCallback(
    (status?: string) => {
      switch (status) {
        case 'approved':
          return 'checkmark-circle';
        case 'pending':
          return 'time';
        case 'declined':
          return 'close-circle';
        default:
          return 'help-circle';
      }
    },
    []
  );



  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <View style={styles.headerLeft}>
          <TouchableOpacity
            accessibilityLabel={t('openMenu')}
            accessibilityRole="button"
            onPress={openDrawer}
            activeOpacity={0.7}
          >
            <Image
              source={require('../../assets/images/logo.png')}
              style={styles.logoImage}
              accessibilityLabel={t('appLogo')}
            />
          </TouchableOpacity>
          <Text style={[styles.appName, { color: colors.text }]}>
            <Text style={{ color: '#52b948' }}>FAS</Text>
            <Text style={{ color: '#f26621' }}>TAG</Text>
            <Text style={{ color: '#817f7f' }}>CAB</Text>
          </Text>
        </View>
        <View style={styles.headerRight}>
          {/* Real-time connection indicator */}
          <View style={[styles.connectionIndicator, { backgroundColor: isSocketConnected ? colors.success : colors.error }]}>
            <Ionicons
              name={isSocketConnected ? "wifi" : "wifi-outline"}
              size={12}
              color="white"
            />
          </View>

          <TouchableOpacity
            style={styles.languageButtonContainer}
            onPress={() => setShowLanguageSwitcher(true)}
            accessibilityLabel={t('changeLanguage')}
            accessibilityRole="button"
          >
            <Ionicons name="language" size={20} color={colors.primary} />
            <Text style={[styles.languageCode, { color: colors.text }]}>
              {i18n.language.toUpperCase()}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.notificationButton}
            onPress={handleNotificationPress}
            accessibilityLabel={`${t('notifications')} (${getUnreadCount()} unread)`}
            accessibilityRole="button"
          >
            <Ionicons name="notifications-outline" size={24} color={colors.primary} />
            {getUnreadCount() > 0 && (
              <View style={[styles.notificationBadge, { backgroundColor: colors.error }]}>
                <Text style={styles.notificationBadgeText}>
                  {getUnreadCount() > 99 ? '99+' : getUnreadCount().toString()}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      </View>



      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
        // refreshControl={
        //   <RefreshControl
        //     refreshing={refreshing}
        //     onRefresh={onRefresh}
        //     colors={[colors.primary]}
        //     tintColor={colors.primary}
        //     title="Pull to refresh points..."
        //     titleColor={colors.textSecondary}
        //     style={styles.refreshToggle}
        //   />
        // }
      >
        {/* Hero Section - User Profile Card */}
        <View style={[styles.heroSection, { backgroundColor: colors.surface, ...theme.shadow }]}>
          <View style={styles.profileRow}>
            <View style={styles.profileImageContainer}>
              <Image
                source={{ uri: user?.profilePhoto || 'https://via.placeholder.com/80' }}
                style={styles.profileImage}
                accessibilityLabel={t('profileImage')}
              />
            </View>
            <View style={styles.profileInfo}>
              <Text style={[styles.welcomeText, { color: colors.text }]}>
                {t('welcome')}, {user?.fullName || 'Guest'}!
              </Text>
              <Text style={[styles.phoneNumber, { color: colors.textSecondary }]}>
                {t('mobileNumber')}: {user?.phoneNumber || 'Not provided'}
              </Text>
              <Text style={[styles.registerDate, { color: colors.textSecondary }]}>
                {t('Role')}: {user?.role || 'Not assigned'}
              </Text>
              <TouchableOpacity
                style={[styles.statusButton, { backgroundColor: getStatusColor(user?.status) }]}
                onPress={() => handleStatusPress(user?.status || 'unknown')}
                accessibilityLabel={`Status: ${user?.status || 'unknown'}`}
                accessibilityRole="button"
              >
                <Ionicons name={getStatusIcon(user?.status) as any} size={16} color="white" />
                <Text style={styles.statusText}>
                  {user?.status ? t(user.status) : 'Unknown'}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Points Summary Section */}
        <View style={[styles.pointsSection, { backgroundColor: colors.surface, ...theme.shadow }]}>
          <View style={styles.pointsRow}>
            <View style={styles.pointsCard}>
              <Text style={[styles.pointsLabel, { color: colors.textSecondary }]}>
                {t('Monthly Points')}
              </Text>
              <Text style={[styles.pointsValue, { color: colors.warning }]}>
                {user?.monthlyPoints || 0}
              </Text>
            </View>
            <View style={styles.pointsCard}>
              <Text style={[styles.pointsLabel, { color: colors.textSecondary }]}>
                {t('Yearly Points')}
              </Text>
              <Text style={[styles.pointsValue, { color: colors.warning }]}>
                {user?.yearlyPoints || 0}
              </Text>
            </View>
          </View>

          {/* Refresh Points Button - for testing */}
          {/* <TouchableOpacity
            style={[styles.refreshButton, { backgroundColor: colors.primary + '20', borderColor: colors.primary }]}
            onPress={onRefresh}
            disabled={refreshing}
            accessibilityLabel="Refresh Points Data"
            accessibilityRole="button"
          >
            <Ionicons
              name={refreshing ? "refresh" : "refresh-outline"}
              size={16}
              color={colors.primary}
              style={refreshing ? { transform: [{ rotate: '180deg' }] } : {}}
            />
            <Text style={[styles.refreshButtonText, { color: colors.primary }]}>
              {refreshing ? 'Refreshing...' : 'Refresh Points'}
            </Text>
          </TouchableOpacity> */}
        </View>

        {/* Auto Sliding Banner */}
        <View style={styles.bannerSection}>
          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onMomentumScrollEnd={(event) => {
              const index = Math.round(event.nativeEvent.contentOffset.x / width);
              setCurrentBannerIndex(index);
            }}
          >
            {bannerImages.map((image, index) => (
              <Image
                key={index}
                source={{ uri: image }}
                style={[styles.bannerImage, { borderRadius: theme.borderRadius.card }]}
                accessibilityLabel={`Banner ${index + 1}`}
              />
            ))}
          </ScrollView>
          <View style={styles.bannerIndicators}>
            {bannerImages.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.indicator,
                  {
                    backgroundColor: index === currentBannerIndex ? colors.primary : colors.border,
                  },
                ]}
              />
            ))}
          </View>
        </View>

        {/* Contact Us Button */}
        <TouchableOpacity
          style={[styles.contactButton, { backgroundColor: colors.primary, borderRadius: theme.borderRadius.button }]}
          onPress={() => router.push('/contact')}
          accessibilityLabel={t('contactUs')}
          accessibilityRole="button"
        >
          <Ionicons name="mail-outline" size={20} color="white" />
          <Text style={styles.contactButtonText}>Contact Us</Text>
        </TouchableOpacity>

        {/* QR Code and Recharge Section */}
        <View style={[styles.qrRechargeSection, { backgroundColor: colors.surface, ...theme.shadow }]}>
          <View style={styles.qrRechargeRow}>
            <TouchableOpacity
              style={[styles.qrButton, { borderColor: colors.primary, backgroundColor: colors.primary + '10' }]}
              onPress={handleQRScan}
              accessibilityLabel={t('qrScanner')}
              accessibilityRole="button"
            >
              <Ionicons name="qr-code" size={40} color={colors.primary} />
              <Text style={[styles.qrButtonText, { color: colors.primary }]}>{t('qrScanner')}</Text>
              <Text style={[styles.qrButtonSubtext, { color: colors.textSecondary }]}>{t('scanQR')}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.rechargeButton, { borderColor: colors.secondary, backgroundColor: colors.secondary + '10' }]}
              // onPress={handleRecharge}
              onPress={() => router.push('/recharge-page')}
              accessibilityLabel={t('recharge')}
              accessibilityRole="button"
            >
              <Ionicons name="card" size={40} color={colors.secondary} />
              <Text style={[styles.rechargeButtonText, { color: colors.secondary }]}>{t('recharge')}</Text>
              <Text style={[styles.rechargeButtonSubtext, { color: colors.textSecondary }]}>{t('enterAmount')}</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.qrRechargeRow}>
            <TouchableOpacity
              style={[styles.qrButton, { borderColor: colors.primary, backgroundColor: colors.primary + '10' }]}
              onPress={() => router.push('/schemes')}
              accessibilityLabel={t('Schemes')}
              accessibilityRole="button"
            >
              <Image
                source={require('../../assets/images/fastagcab.png')}
                style={styles.schemeImage}
              />
              <Text style={[styles.qrButtonText, { color: colors.primary }]}>
                {t('Schemes')}
              </Text>
              <Text style={[styles.qrButtonSubtext, { color: colors.textSecondary }]}>
                {t('FASTAGCAB')}
              </Text>

            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.rechargeButton, { borderColor: colors.secondary, backgroundColor: colors.secondary + '10' }]}
              onPress={() => router.push('/dthschemes')}
              accessibilityLabel={t('dthschemes')}
              accessibilityRole="button"
            >
              <Image
                source={require('../../assets/images/dth.jpg')}
                style={styles.schemeImage}
              />
              <Text style={[styles.rechargeButtonText, { color: colors.secondary }]}>
                {t('Categories')}
              </Text>
              <Text style={[styles.rechargeButtonSubtext, { color: colors.textSecondary }]}>
                {t('DDH')}
              </Text>

            </TouchableOpacity>
          </View>
        </View>

        {/* Bottom Section */}
        <View style={styles.bottomSection}>
          {/* Offers */}
          <View style={[styles.offersSection, { backgroundColor: colors.surface, ...theme.shadow }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('offers')}</Text>
            {isLoadingOffers ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color={colors.primary} />
                <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading offers...</Text>
              </View>
            ) : (
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                {offers.length > 0 ? offers.map((offer) => (
                  <View key={offer._id} style={[styles.offerCard, { backgroundColor: colors.card, borderColor: colors.border, borderRadius: theme.borderRadius.card }]}>
                    <Image
                      source={{ uri: offer.imageUrl.startsWith('http') ? offer.imageUrl : `${backendUrl}${offer.imageUrl}` }}
                      style={styles.offerImage}
                    />
                    <Text style={[styles.offerTitle, { color: colors.text }]}>{offer.title}</Text>
                    <Text style={[styles.offerDescription, { color: colors.textSecondary }]} numberOfLines={2}>
                      {offer.description}
                    </Text>
                  </View>
                )) : (
                  // Fallback offers when no data from API
                  [
                    { id: '1', title: 'Special Offer 1', description: 'Get up to 50% off on recharges', image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=200&fit=crop&crop=center&auto=format&q=80' },
                    { id: '2', title: 'Special Offer 2', description: 'Get up to 30% off on recharges', image: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=200&fit=crop&crop=center&auto=format&q=80' },
                    { id: '3', title: 'Special Offer 3', description: 'Get up to 20% off on recharges', image: 'https://images.unsplash.com/photo-1556740758-90de374c12ad?w=400&h=200&fit=crop&crop=center&auto=format&q=80' },
                  ].map((offer) => (
                    <View key={offer.id} style={[styles.offerCard, { backgroundColor: colors.card, borderColor: colors.border, borderRadius: theme.borderRadius.card }]}>
                      <Image
                        source={{ uri: offer.image }}
                        style={styles.offerImage}
                      />
                      <Text style={[styles.offerTitle, { color: colors.text }]}>{offer.title}</Text>
                      <Text style={[styles.offerDescription, { color: colors.textSecondary }]}>
                        {offer.description}
                      </Text>
                    </View>
                  ))
                )}
              </ScrollView>
            )}
          </View>

          {/* Products */}
          <View style={[styles.productsSection, { backgroundColor: colors.surface, ...theme.shadow }]}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>{t('Products')}</Text>
            {isLoadingProducts ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading products...</Text>
              </View>
            ) : (
              <FlatList
                data={products}
                keyExtractor={(item) => item._id}
                renderItem={({ item }) => (
                  <View
                    style={[styles.productCard, { backgroundColor: colors.card, borderColor: colors.border, borderRadius: theme.borderRadius.card }]}
                  >
                    <View style={styles.itemImageContainer}>
                      <Image
                        source={{
                          uri: item.primaryImage ||
                               (item.images && item.images.length > 0
                                 ? (item.images[0].url.startsWith('http') ? item.images[0].url : `${backendUrl}${item.images[0].url}`)
                                 : 'https://via.placeholder.com/110x110?text=No+Image'
                               )
                        }}
                        style={[styles.productImage, { borderRadius: theme.borderRadius.image }]}
                        accessibilityLabel={item.name}
                      />
                    </View>
                    <View style={styles.itemDetailContainer}>
                      <Text style={[styles.productName, { color: colors.text }]}>{item.name}</Text>
                      <Text style={[styles.productDescription, { color: colors.textSecondary }]} numberOfLines={2}>
                        {item.shortDescription || item.description}
                      </Text>
                      {item.price && (
                        <Text style={[styles.productPrice, { color: colors.primary }]}>
                          ₹{item.price.toLocaleString()}
                          {item.originalPrice && item.originalPrice > item.price && (
                            <Text style={[styles.originalPrice, { color: colors.textSecondary }]}>
                              {' '}₹{item.originalPrice.toLocaleString()}
                            </Text>
                          )}
                        </Text>
                      )}
                    </View>
                  </View>
                )}
                numColumns={1}
                contentContainerStyle={styles.productsGrid}
                ListEmptyComponent={() => (
                  <Text style={[styles.emptyText, { color: colors.textSecondary }]}>No products available</Text>
                )}
              />
            )}
          </View>
        </View>
      </ScrollView>

      {/* WhatsApp Floating Button */}
      <View style={styles.whatsappButtonContainer}>
        <WhatsAppButton style={{ backgroundColor: colors.primary}} />
      </View>

      {/* Modals */}
      <QRScanner visible={showQRScanner} onClose={() => setShowQRScanner(false)} />
      <LanguageSwitcher visible={showLanguageSwitcher} onClose={() => setShowLanguageSwitcher(false)} />

      {/* Notification Modal */}
      <Modal
        visible={showNotificationModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowNotificationModal(false)}
      >
        <SafeAreaView style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.modalHeader, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>Notifications</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={() => setShowNotificationModal(false)}
              accessibilityLabel={t('close')}
              accessibilityRole="button"
            >
              <Ionicons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
          <View style={styles.modalContent}>
            {isLoadingNotifications ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={[styles.loadingText, { color: colors.textSecondary }]}>Loading notifications...</Text>
              </View>
            ) : notificationError ? (
              <View style={styles.errorContainer}>
                <Ionicons name="alert-circle" size={48} color={colors.error} />
                <Text style={[styles.errorText, { color: colors.error }]}>{notificationError}</Text>
                <TouchableOpacity
                  style={[styles.retryButton, { backgroundColor: colors.primary, borderRadius: theme.borderRadius.button }]}
                  onPress={fetchNotifications}
                  accessibilityLabel={t('retry')}
                  accessibilityRole="button"
                >
                  <Text style={styles.retryButtonText}>Retry</Text>
                </TouchableOpacity>
              </View>
            ) : (
              <FlatList
                data={notifications}
                keyExtractor={(item) => item.id}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.notificationsList}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={[
                      styles.notificationItem,
                      {
                        backgroundColor: colors.surface,
                        borderLeftColor: item.read ? colors.border : colors.primary,
                        borderRadius: theme.borderRadius.card,
                      },
                    ]}
                    onPress={() => markNotificationAsRead(item.id)}
                    accessibilityLabel={`${item.title} ${item.read ? '' : t('unread')}`}
                    accessibilityRole="button"
                  >
                    <View style={styles.notificationHeader}>
                      <View style={styles.notificationIconContainer}>
                        <Ionicons
                          name={
                            item.type === 'success'
                              ? 'checkmark-circle'
                              : item.type === 'warning'
                              ? 'warning'
                              : item.type === 'error'
                              ? 'close-circle'
                              : 'information-circle'
                          }
                          size={24}
                          color={
                            item.type === 'success'
                              ? colors.primary
                              : item.type === 'warning'
                              ? colors.warning
                              : item.type === 'error'
                              ? colors.error
                              : colors.info
                          }
                        />
                      </View>
                      <View style={styles.notificationContent}>
                        <Text
                          style={[styles.notificationTitle, { color: colors.text }, !item.read && styles.unreadNotificationTitle]}
                        >
                          {item.title}
                        </Text>
                        <Text style={[styles.notificationMessage, { color: colors.textSecondary }]}>{item.message}</Text>
                        <Text style={[styles.notificationTimestamp, { color: colors.textSecondary }]}>
                          {new Date(item.timestamp).toLocaleString()}
                        </Text>
                      </View>
                      {!item.read && <View style={[styles.unreadDot, { backgroundColor: colors.primary }]} />}
                    </View>
                  </TouchableOpacity>
                )}
                ListEmptyComponent={() => (
                  <View style={styles.emptyNotifications}>
                    <Ionicons name="notifications-off" size={64} color={colors.textSecondary} />
                    <Text style={[styles.emptyNotificationsText, { color: colors.textSecondary }]}>No notifications yet</Text>
                  </View>
                )}
              />
            )}
          </View>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  connectionIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  languageButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  languageCode: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  appName: {
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  notificationButton: {
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: -6,
    right: -6,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  notificationBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  heroSection: {
    margin: 16,
    borderRadius: theme.borderRadius.card,
    padding: 24,
  },
  profileRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileImageContainer: {
    marginRight: 16,
  },
  profileImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  profileInfo: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  phoneNumber: {
    fontSize: 14,
    marginBottom: 2,
  },
  registerDate: {
    fontSize: 14,
    marginBottom: 8,
  },
  statusButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
    textTransform: 'capitalize',
  },
  pointsSection: {
    margin: 16,
    marginTop: 0,
    borderRadius: theme.borderRadius.card,
    padding: 24,
  },
  pointsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  pointsCard: {
    flex: 1,
    alignItems: 'center',
    marginHorizontal: 8,
  },
  pointsLabel: {
    fontSize: 14,
    marginBottom: 4,
    textAlign: 'center',
  },
  pointsValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  bannerSection: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  bannerImage: {
    width: width - 32,
    height: 180,
  },
  bannerIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 8,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 16,
    marginBottom: 16,
    paddingVertical: 16,
  },
  contactButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  qrRechargeSection: {
    margin: 16,
    marginTop: 0,
    borderRadius: 20,
    backgroundColor: '#fff',
    padding: 24,
  },
  qrRechargeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    margin: 5,
    marginBottom: 15,

  },
  qrButton: {
    flex: 1,
    alignItems: 'center',
    padding: 20,
    borderWidth: 1,
    borderRadius: theme.borderRadius.card,
    marginRight: 8,
  },
  qrButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
  },
  qrButtonSubtext: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
  rechargeButton: {
    flex: 1,
    alignItems: 'center',
    padding: 20,
    borderWidth: 1,
    borderRadius: theme.borderRadius.card,
    marginLeft: 8,
  },
  rechargeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
  },
  rechargeButtonSubtext: {
    fontSize: 12,
    marginTop: 4,
    textAlign: 'center',
  },
  bottomSection: {
    paddingBottom: 80, // Increased padding to prevent overlap with WhatsApp button
  },
  offersSection: {
    margin: 16,
    borderRadius: theme.borderRadius.card,
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  offerCard: {
    width: 200,
    padding: 16,
    borderWidth: 1,
    marginRight: 12,
  },
  offerTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  offerImage: {
    width: '100%',
    height: 120,
    borderRadius: 8,
    marginBottom: 8,

  },
  offerDescription: {
    fontSize: 14,
  },
  productsSection: {
    margin: 16,
    borderRadius: theme.borderRadius.card,
    padding: 20,
  },
  productsGrid: {
    paddingBottom: 16,
    flexDirection: 'column',
    justifyContent: 'space-between',

  },
  productCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,

    borderWidth: 1,
    marginBottom: 12,
    width: '100%',
  },
  productImage: {
    width: 110,
    height: 110,
    marginLeft: 14,
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
    marginLeft: 14,
  },
  productDescription: {
    fontSize: 14,
    marginLeft: 14,
    lineHeight: 20,

  },
  logoImage: {
    width: 44,
    height: 44,
  },
  modalContainer: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 8,
  },
  modalContent: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 16,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  notificationsList: {
    paddingVertical: 8,
  },
  notificationItem: {
    marginHorizontal: 16,
    marginVertical: 4,
    borderLeftWidth: 4,
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
  },
  notificationIconContainer: {
    marginRight: 12,
    marginTop: 2,
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  unreadNotificationTitle: {
    fontWeight: 'bold',
  },
  notificationMessage: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 4,
  },
  notificationTimestamp: {
    fontSize: 12,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginTop: 4,
  },
  emptyNotifications: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 64,
  },
  emptyNotificationsText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  itemImageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemDetailContainer: {
    flex: 2,
    paddingLeft: 20,
    justifyContent: 'center',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
  },
  whatsappButtonContainer: {
    position: 'absolute',
    bottom: 30,
    right: 30,
    zIndex: 1000,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  schemeImage: {
    width: 80,
    height: 50,
    marginBottom: 8,
  },
  refreshButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
  },
  refreshButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  refreshToggle:{
    // display:none,
  },
  productPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 4,
    marginLeft: 14,
  },
  originalPrice: {
    fontSize: 14,
    textDecorationLine: 'line-through',
    fontWeight: 'normal',
  },
});

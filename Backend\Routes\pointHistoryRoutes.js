import express from 'express';
import { body } from 'express-validator';
import {
  getUserPointHistory,
  getUserPointStats,
  getAllUsersPointHistory,
  adjustUserPoints,
  getPointHistoryAnalytics
} from '../controllers/pointHistoryController.js';
import {
  authenticateToken,
  requireAdmin
} from '../middleware/auth.js';

const router = express.Router();

// @route   GET api/points/history
// @desc    Get user's point history
// @access  Private
router.get('/history', authenticateToken, getUserPointHistory);

// @route   GET api/points/stats
// @desc    Get user's point statistics
// @access  Private
router.get('/stats', authenticateToken, getUserPointStats);

// @route   GET api/points/history/all
// @desc    Get all users' point history (Admin only)
// @access  Private (Admin only)
router.get('/history/all', authenticateToken, requireAdmin, getAllUsersPointHistory);

// @route   POST api/points/adjust
// @desc    Create manual point adjustment (Admin only)
// @access  Private (Admin only)
router.post(
  '/adjust',
  authenticateToken,
  requireAdmin,
  [
    body('userId')
      .notEmpty()
      .withMessage('User ID is required')
      .isMongoId()
      .withMessage('Invalid user ID format'),
    body('pointsChange')
      .isInt()
      .withMessage('Points change must be an integer')
      .custom((value) => {
        if (value === 0) {
          throw new Error('Points change cannot be zero');
        }
        return true;
      }),
    body('reason')
      .notEmpty()
      .withMessage('Reason is required')
      .isLength({ min: 3, max: 500 })
      .withMessage('Reason must be between 3 and 500 characters'),
    body('adjustmentType')
      .isIn(['monthly', 'yearly'])
      .withMessage('Adjustment type must be either monthly or yearly')
  ],
  adjustUserPoints
);

// @route   GET api/points/analytics
// @desc    Get point history analytics (Admin only)
// @access  Private (Admin only)
router.get('/analytics', authenticateToken, requireAdmin, getPointHistoryAnalytics);

export default router;
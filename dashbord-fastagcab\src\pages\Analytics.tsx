import React, { useState, useEffect } from 'react'
import { TrendingUp, Users, Award, Calendar, RefreshCw, AlertCircle } from 'lucide-react'
import Card, { CardHeader, CardTitle, CardContent } from '../components/ui/Card'
import Button from '../components/ui/Button'
import LoadingSpinner from '../components/ui/LoadingSpinner'
import { analyticsAPI } from '../lib/api'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell, AreaChart, Area } from 'recharts'
import toast from 'react-hot-toast'

const Analytics: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState('30d')
  const [analyticsData, setAnalyticsData] = useState<any>(null)

  useEffect(() => {
    fetchAnalyticsData()
  }, [timeRange])

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Try to fetch real analytics data
      try {
        const [userAnalytics, pointsAnalytics] = await Promise.all([
          analyticsAPI.getUserStats(timeRange),
          analyticsAPI.getPointsStats(timeRange)
        ])

        if (userAnalytics.success && pointsAnalytics.success) {
          setAnalyticsData({
            userAnalytics: userAnalytics.data,
            pointsAnalytics: pointsAnalytics.data
          })
          toast.success('Analytics data loaded successfully')
        } else {
          throw new Error('Failed to fetch analytics data')
        }
      } catch (error: any) {
        console.error('Failed to fetch analytics:', error)
        const message = error.response?.data?.message || 'Failed to load analytics data, showing sample data'
        setError(message)
        toast.error(message)

        // Set sample data as fallback
        setAnalyticsData(null)
      }
    } catch (error: any) {
      console.error('Analytics error:', error)
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  // Mock data - replace with actual API calls
  const userGrowthData = [
    { month: 'Jan', users: 120, electricians: 80, distributors: 40 },
    { month: 'Feb', users: 150, electricians: 100, distributors: 50 },
    { month: 'Mar', users: 180, electricians: 120, distributors: 60 },
    { month: 'Apr', users: 220, electricians: 150, distributors: 70 },
    { month: 'May', users: 280, electricians: 190, distributors: 90 },
    { month: 'Jun', users: 320, electricians: 220, distributors: 100 },
  ]

  const pointsData = [
    { month: 'Jan', points: 15000 },
    { month: 'Feb', points: 18000 },
    { month: 'Mar', points: 22000 },
    { month: 'Apr', points: 25000 },
    { month: 'May', points: 28000 },
    { month: 'Jun', points: 32000 },
  ]

  const registrationsByDay = [
    { day: 'Mon', registrations: 12 },
    { day: 'Tue', registrations: 8 },
    { day: 'Wed', registrations: 15 },
    { day: 'Thu', registrations: 10 },
    { day: 'Fri', registrations: 18 },
    { day: 'Sat', registrations: 6 },
    { day: 'Sun', registrations: 4 },
  ]

  const statusDistribution = [
    { name: 'Approved', value: 65, color: '#22c55e' },
    { name: 'Pending', value: 25, color: '#f59e0b' },
    { name: 'Rejected', value: 10, color: '#ef4444' },
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
            <p className="text-gray-600 mt-1">Insights and performance metrics</p>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <LoadingSpinner size="lg" />
          <p className="text-gray-500">Loading analytics data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
            <p className="text-gray-600 mt-1">Insights and performance metrics</p>
          </div>
          <Button
            onClick={fetchAnalyticsData}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Retry
          </Button>
        </div>
        <div className="flex flex-col items-center justify-center h-64 space-y-4">
          <AlertCircle className="h-12 w-12 text-red-500" />
          <div className="text-center">
            <p className="text-gray-900 font-medium">Failed to load analytics data</p>
            <p className="text-gray-500 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
          <p className="text-gray-600 mt-1">Insights and performance metrics</p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={fetchAnalyticsData}
            variant="outline"
            size="sm"
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <select
            className="input w-40"
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <div className="flex items-center p-6">
            <div className="p-3 rounded-lg bg-blue-100">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">1,234</p>
              <p className="text-xs text-green-600">+12% from last month</p>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="flex items-center p-6">
            <div className="p-3 rounded-lg bg-green-100">
              <TrendingUp className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Growth Rate</p>
              <p className="text-2xl font-bold text-gray-900">15.3%</p>
              <p className="text-xs text-green-600">+2.1% from last month</p>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="flex items-center p-6">
            <div className="p-3 rounded-lg bg-yellow-100">
              <Award className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Points</p>
              <p className="text-2xl font-bold text-gray-900">156K</p>
              <p className="text-xs text-green-600">+8.2% from last month</p>
            </div>
          </div>
        </Card>
        
        <Card>
          <div className="flex items-center p-6">
            <div className="p-3 rounded-lg bg-purple-100">
              <Calendar className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg. Daily Registrations</p>
              <p className="text-2xl font-bold text-gray-900">12</p>
              <p className="text-xs text-green-600">+5.4% from last week</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Growth */}
        <Card>
          <CardHeader>
            <CardTitle>User Growth Over Time</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={userGrowthData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Area type="monotone" dataKey="users" stackId="1" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.6} />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Points Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Points Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={pointsData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="points" stroke="#10b981" strokeWidth={3} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Daily Registrations */}
        <Card>
          <CardHeader>
            <CardTitle>Daily Registrations (This Week)</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={registrationsByDay}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="registrations" fill="#f59e0b" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* User Status Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>User Status Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={statusDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {statusDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* User Types Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>User Types Growth</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={userGrowthData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="electricians" stackId="a" fill="#3b82f6" name="Electricians" />
              <Bar dataKey="distributors" stackId="a" fill="#10b981" name="Distributors" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  )
}

export default Analytics

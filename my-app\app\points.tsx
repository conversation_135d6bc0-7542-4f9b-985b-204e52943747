import { useTheme } from '@/contexts/ThemeContext';
import { useAuth } from '@/contexts/AuthContext';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

export default function PointsScreen() {
  const { colors, theme } = useTheme();
  const { user } = useAuth();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.surface, borderBottomColor: colors.border }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.primary} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>My Points</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView contentContainerStyle={styles.content}>
        {/* Points Summary Card */}
        <View style={[styles.pointsCard, { backgroundColor: colors.surface, ...theme.shadow }]}>
          <View style={styles.pointsHeader}>
            <View style={[styles.iconContainer, { backgroundColor: colors.primary + '15' }]}>
              <Ionicons name="diamond" size={32} color={colors.primary} />
            </View>
            <Text style={[styles.pointsTitle, { color: colors.text }]}>Your Points</Text>
          </View>

          <View style={styles.pointsRow}>
            <View style={styles.pointItem}>
              <Text style={[styles.pointValue, { color: colors.warning }]}>
                {user?.monthlyPoints || 0}
              </Text>
              <Text style={[styles.pointLabel, { color: colors.textSecondary }]}>
                Monthly Points
              </Text>
            </View>
            <View style={styles.pointItem}>
              <Text style={[styles.pointValue, { color: colors.success }]}>
                {user?.yearlyPoints || 0}
              </Text>
              <Text style={[styles.pointLabel, { color: colors.textSecondary }]}>
                Yearly Points
              </Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.primary, ...theme.shadow }]}
            onPress={() => router.push('/point-history')}
          >
            <Ionicons name="time" size={24} color={colors.background} />
            <Text style={[styles.actionButtonText, { color: colors.background }]}>
              Point History
            </Text>
            <Ionicons name="chevron-forward" size={20} color={colors.background} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.surface, ...theme.shadow }]}
            onPress={() => router.push('/(tabs)/gift')}
          >
            <Ionicons name="gift" size={24} color={colors.primary} />
            <Text style={[styles.actionButtonText, { color: colors.text }]}>
              Redeem Gifts
            </Text>
            <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: colors.surface, ...theme.shadow }]}
            onPress={() => router.push('/scanner')}
          >
            <Ionicons name="qr-code" size={24} color={colors.primary} />
            <Text style={[styles.actionButtonText, { color: colors.text }]}>
              Scan QR Code
            </Text>
            <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        </View>

        {/* Info Card */}
        <View style={[styles.infoCard, { backgroundColor: colors.surface, ...theme.shadow }]}>
          <View style={[styles.infoIconContainer, { backgroundColor: colors.info + '15' }]}>
            <Ionicons name="information-circle" size={24} color={colors.info} />
          </View>
          <View style={styles.infoContent}>
            <Text style={[styles.infoTitle, { color: colors.text }]}>How to Earn Points</Text>
            <Text style={[styles.infoDescription, { color: colors.textSecondary }]}>
              • Scan QR codes on FASTAGCAB products{'\n'}
              • Complete monthly challenges{'\n'}
              • Refer friends to the app{'\n'}
              • Participate in special promotions
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 40,
  },
  content: {
    padding: 16,
  },
  pointsCard: {
    borderRadius: 16,
    padding: 24,
    marginBottom: 20,
  },
  pointsHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  pointsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  pointsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  pointItem: {
    alignItems: 'center',
  },
  pointValue: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  pointLabel: {
    fontSize: 14,
    textAlign: 'center',
  },
  actionsContainer: {
    marginBottom: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  actionButtonText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  infoCard: {
    flexDirection: 'row',
    padding: 16,
    borderRadius: 12,
  },
  infoIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  infoContent: {
    flex: 1,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  infoDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
});

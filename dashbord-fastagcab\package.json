{"name": "dashbord-fastagcab", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "jszip": "^3.10.1", "lucide-react": "^0.525.0", "qrcode": "^1.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-qr-code": "^2.0.18", "react-router-dom": "^7.7.0", "recharts": "^3.1.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.11", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "^5.8.3", "vite": "^7.0.4"}}
import express from "express";
import dotenv from "dotenv";
import cookieParser from "cookie-parser";
import cors from "cors";
import path from "path";
import mongoose from "mongoose";
import { fileURLToPath } from 'url';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dbConnect from "./Db/dbConnect.js";
import authRoutes from "./Routes/authroute.js";
import userRoutes from "./Routes/userRoutes.js";
import dataRoutes from "./Routes/dataRoutes.js";
import qrCodeRoutes from "./Routes/qrcodes.js";
import adminRoutes from "./Routes/adminRoutes.js";
import contentRoutes from "./Routes/contentRoutes.js";
import giftRedemptionRoutes from "./Routes/giftRedemptionRoutes.js";
import notificationRoutes from "./Routes/notificationRoutes.js";
import contactRoutes from "./Routes/contactRoutes.js";
import pointHistoryRoutes from "./Routes/pointHistoryRoutes.js";
import rechargeAdminRoutes from "./Routes/rechargeAdminRoutes.js";
import scheduledTaskService from "./services/scheduledTaskService.js";
import { errorHandler, notFound } from "./middleware/errorHandler.js";
import SocketService from "./services/socketService.js";

// Get __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
console.log('🚀 Starting backend server...');
dotenv.config();
console.log('📁 Environment variables loaded');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Initialize Socket service
const socketService = new SocketService(io);

// Make socket service globally available
global.socketService = socketService;

const PORT = process.env.PORT || 5000;

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

app.use(cors({
    origin: true, // Allow all origins for React Native
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'Accept',
        'X-Requested-With',
        'Access-Control-Allow-Origin',
        'Access-Control-Allow-Headers',
        'Access-Control-Allow-Methods'
    ],
    credentials: false, // Set to false when allowing all origins
    maxAge: 86400,
    optionsSuccessStatus: 200 // For legacy browser support
}));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Ensure upload directories exist
import fs from 'fs';
const uploadDirs = ['uploads/profiles', 'uploads/documents', 'uploads/content'];
uploadDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`📁 Created directory: ${dir}`);
  }
});

// Additional CORS headers for React Native
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// Health check route
app.get("/", (_req, res) => {
    const dbStatus = mongoose.connection.readyState === 1 ? 'connected' : 'disconnected';
    res.status(200).json({
        success: true,
        message: "API is running successfully! check message",
        version: "1.0.0",
        timestamp: new Date().toISOString(),
        database: dbStatus
    });
});

// API Routes - Add them one by one to test
console.log('🔗 Registering /api/auth routes');
app.use('/api/auth', authRoutes);
console.log('🔗 Registering /api/users routes');
app.use('/api/users', userRoutes);
app.use('/api/data', dataRoutes);
app.use("/api/qrcodes", qrCodeRoutes);
app.use("/api/qr", qrCodeRoutes); // Additional route for mobile app compatibility
app.use('/api/gifts', giftRedemptionRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/contact', contactRoutes);
app.use('/api/points', pointHistoryRoutes);

app.use('/api/admin', adminRoutes);
app.use('/api/content', contentRoutes);

// Backward compatibility routes for mobile app
app.use('/api/banners', contentRoutes);
app.use('/api/offers', contentRoutes);
app.use('/api/products', contentRoutes);

// Recharge admin routes (commented out due to issues)
// app.use('/api/recharges', rechargeAdminRoutes);

app.use(notFound);
app.use(errorHandler);

app.get('/mytest', (req, res) => {
  res.json({ message: 'Login route reached' });
});

// Serve Socket.IO test page
app.get('/test-socket', (req, res) => {
  res.sendFile(path.join(__dirname, 'test-socket.html'));
});
// Start server - Listen on all network interfaces
server.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🌐 Local Server URL: http://localhost:${PORT}`);
    // console.log(`🌐 Network Server URL: http://**************:${PORT}`);
    // console.log(`📱 Apps should use: https://fastag.bd1.pro/`);
    console.log(`�📋 Available endpoints:`);
    console.log(`  GET  /`);
    console.log(`  POST api/auth/send-otp`);
    console.log(`  POST api/auth/verify-otp`);
    console.log(`  POST api/auth/resend-otp`);
    console.log(`  POST api/auth/login`);
    console.log(`  POST api/auth/register`);
    console.log(`  POST api/auth/logout`);
    console.log(`  GET  api/auth/test`);
    console.log(`✅ Server is ready to accept requests from all network interfaces!`);

    // Connect to database
    dbConnect();

    // Initialize scheduled tasks after database connection
    setTimeout(() => {
        scheduledTaskService.init();
    }, 2000); // Wait 2 seconds for database to be ready
});

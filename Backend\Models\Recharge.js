import mongoose from 'mongoose';

const rechargeSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
    index: true
  },
  mobileNumber: {
    type: String,
    required: [true, 'Mobile number is required'],
    trim: true,
    match: [/^[6-9]\d{9}$/, 'Please enter a valid 10-digit mobile number']
  },
  operator: {
    type: String,
    required: [true, 'Operator is required'],
    trim: true,
    maxlength: [50, 'Operator name cannot exceed 50 characters']
  },
  rechargeAmount: {
    type: String,
    required: [true, 'Recharge amount is required'],
    trim: true
  },
  rechargeDuration: {
    type: Number,
    required: [true, 'Recharge duration is required'],
    min: [1, 'Duration must be at least 1 month']
  },
  rechargeType: {
    type: String,
    enum: ['monthly_plan', 'data_pack', 'talk_time', 'special_offer'],
    default: 'monthly_plan'
  },
  pointsDeducted: {
    type: Number,
    required: [true, 'Points deducted is required'],
    min: [500, 'Minimum 500 points required for recharge']
  },
  userMonthlyPointsAtRecharge: {
    type: Number,
    required: [true, 'User monthly points at recharge is required'],
    min: [0, 'User monthly points cannot be negative']
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed', 'cancelled'],
    default: 'pending',
    index: true
  },
  rechargeDate: {
    type: Date,
    default: Date.now
  },
  processedDate: {
    type: Date,
    default: null
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  adminNotes: {
    type: String,
    trim: true,
    maxlength: [500, 'Admin notes cannot exceed 500 characters']
  },
  transactionId: {
    type: String,
    trim: true,
    unique: true,
    sparse: true
  },
  apiResponse: {
    type: mongoose.Schema.Types.Mixed,
    default: null
  },
  // Notification tracking
  userNotificationSent: {
    type: Boolean,
    default: false
  },
  dashboardNotificationSent: {
    type: Boolean,
    default: false
  },
  userNotificationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Notification',
    default: null
  },
  dashboardNotificationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Notification',
    default: null
  },
  // Additional metadata
  metadata: {
    userAgent: String,
    ipAddress: String,
    deviceInfo: String,
    requestSource: {
      type: String,
      default: 'mobile_app'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
rechargeSchema.index({ userId: 1, status: 1 });
rechargeSchema.index({ status: 1, createdAt: -1 });
rechargeSchema.index({ rechargeDate: -1 });
rechargeSchema.index({ mobileNumber: 1 });

// Virtual for checking if recharge is still pending
rechargeSchema.virtual('isPending').get(function() {
  return this.status === 'pending';
});

// Virtual for checking if recharge is completed
rechargeSchema.virtual('isCompleted').get(function() {
  return this.status === 'completed';
});

// Virtual for formatted recharge date
rechargeSchema.virtual('formattedRechargeDate').get(function() {
  return this.rechargeDate.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
});

// Instance method to mark recharge as completed
rechargeSchema.methods.markCompleted = function(transactionId = null, apiResponse = null) {
  this.status = 'completed';
  this.processedDate = new Date();
  if (transactionId) this.transactionId = transactionId;
  if (apiResponse) this.apiResponse = apiResponse;
  return this.save();
};

// Instance method to mark recharge as failed
rechargeSchema.methods.markFailed = function(reason = null) {
  this.status = 'failed';
  this.processedDate = new Date();
  if (reason) this.adminNotes = reason;
  return this.save();
};

// Static method to get user's recharge history
rechargeSchema.statics.getUserRecharges = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    status,
    startDate,
    endDate,
    sortBy = 'createdAt',
    sortOrder = -1
  } = options;

  const query = { userId };
  
  if (status) query.status = status;
  if (startDate || endDate) {
    query.createdAt = {};
    if (startDate) query.createdAt.$gte = new Date(startDate);
    if (endDate) query.createdAt.$lte = new Date(endDate);
  }

  return this.find(query)
    .populate('userId', 'fullName phoneNumber')
    .populate('processedBy', 'fullName')
    .sort({ [sortBy]: sortOrder })
    .limit(limit * 1)
    .skip((page - 1) * limit)
    .exec();
};

// Static method to get recharge statistics
rechargeSchema.statics.getRechargeStats = function(userId, startDate = null) {
  const matchQuery = { userId: new mongoose.Types.ObjectId(userId) };
  
  if (startDate) {
    matchQuery.createdAt = { $gte: startDate };
  }

  return this.aggregate([
    {
      $match: matchQuery
    },
    {
      $group: {
        _id: '$status',
        totalRecharges: { $sum: 1 },
        totalPointsSpent: { $sum: '$pointsDeducted' },
        avgPointsPerRecharge: { $avg: '$pointsDeducted' }
      }
    }
  ]);
};

// Pre-save middleware to generate transaction ID
rechargeSchema.pre('save', function(next) {
  if (this.isNew && !this.transactionId) {
    this.transactionId = `RCH${Date.now()}${Math.random().toString(36).substr(2, 5).toUpperCase()}`;
  }
  next();
});

const Recharge = mongoose.models.Recharge || mongoose.model('Recharge', rechargeSchema);

export default Recharge;

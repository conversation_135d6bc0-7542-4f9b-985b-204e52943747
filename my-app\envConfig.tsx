const getServerBaseUrl = (): string => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  console.log("node env is ",process.env.NODE_ENV)
  
  const LOCAL_SERVER_URL = process.env.LOCAL_SERVER_URL || 'http://localhost:5000';
  const PRODUCTION_SERVER_URL = process.env.PRODUCTION_SERVER_URL || 'https://fastag.bd1.pro';
  
  return isDevelopment ? LOCAL_SERVER_URL : PRODUCTION_SERVER_URL;
};

export default getServerBaseUrl;
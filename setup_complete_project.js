import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = colors.reset) => {
  console.log(`${color}${message}${colors.reset}`);
};

const runCommand = (command, args, cwd, description) => {
  return new Promise((resolve, reject) => {
    log(`\n🔄 ${description}...`, colors.cyan);
    log(`📂 Directory: ${cwd}`, colors.yellow);
    log(`⚡ Command: ${command} ${args.join(' ')}`, colors.blue);
    
    const process = spawn(command, args, { 
      cwd, 
      stdio: 'inherit',
      shell: true 
    });
    
    process.on('close', (code) => {
      if (code === 0) {
        log(`✅ ${description} completed successfully`, colors.green);
        resolve();
      } else {
        log(`❌ ${description} failed with code ${code}`, colors.red);
        reject(new Error(`${description} failed`));
      }
    });
    
    process.on('error', (error) => {
      log(`❌ Error in ${description}: ${error.message}`, colors.red);
      reject(error);
    });
  });
};

const checkFileExists = (filePath) => {
  return fs.existsSync(filePath);
};

const createEnvFile = (filePath, content) => {
  if (!checkFileExists(filePath)) {
    fs.writeFileSync(filePath, content);
    log(`✅ Created ${filePath}`, colors.green);
  } else {
    log(`ℹ️ ${filePath} already exists`, colors.yellow);
  }
};

const setupProject = async () => {
  try {
    log('🚀 FASTAGCAB Complete Project Setup', colors.bright + colors.magenta);
    log('=' * 50, colors.magenta);

    const rootDir = process.cwd();
    const backendDir = path.join(rootDir, 'Backend');
    const reactNativeDir = path.join(rootDir, 'my-app');
    const dashboardDir = path.join(rootDir, 'dashbord-fastagcab');

    // Check directories exist
    const dirs = [
      { path: backendDir, name: 'Backend' },
      { path: reactNativeDir, name: 'React Native App' },
      { path: dashboardDir, name: 'Admin Dashboard' }
    ];

    log('\n📁 Checking project structure...', colors.cyan);
    for (const dir of dirs) {
      if (checkFileExists(dir.path)) {
        log(`✅ ${dir.name}: ${dir.path}`, colors.green);
      } else {
        log(`❌ ${dir.name} not found: ${dir.path}`, colors.red);
        throw new Error(`Missing directory: ${dir.path}`);
      }
    }

    // Create environment files
    log('\n🔧 Setting up environment files...', colors.cyan);
    
    // Backend .env
//     const backendEnv = `PORT=5000
// CLIENT_URL=exp://***************:8081
// MONGO_URI=mongodb+srv://ankitgangrade9617:<EMAIL>/electrician-app?retryWrites=true&w=majority&appName=Cluster0&serverSelectionTimeoutMS=30000&socketTimeoutMS=45000&connectTimeoutMS=30000&maxPoolSize=10&minPoolSize=5
// JWT_SECRET=FASTAGCABAPP-super-secret-key
// JWT_EXPIRES_IN=7d
// MAX_FILE_SIZE=5242880
// UPLOAD_PATH=uploads/
// NODE_ENV=production

// # Redis Configuration (optional)
// REDIS_HOST=localhost
// REDIS_PORT=6379`;

    createEnvFile(path.join(backendDir, '.env'), backendEnv);

    // Dashboard .env
    // const dashboardEnv = `VITE_API_URL=https://fastag.bd1.pro/api`;
    createEnvFile(path.join(dashboardDir, '.env'), dashboardEnv);

    // Install dependencies
    log('\n📦 Installing dependencies...', colors.cyan);
    
    await runCommand('npm', ['install'], backendDir, 'Backend dependencies installation');
    await runCommand('npm', ['install'], reactNativeDir, 'React Native dependencies installation');
    await runCommand('npm', ['install'], dashboardDir, 'Dashboard dependencies installation');

    log('\n🎉 Setup completed successfully!', colors.bright + colors.green);
    log('\n📋 Next steps:', colors.cyan);
    log('1. Start Backend: cd Backend && npm run dev', colors.yellow);
    log('2. Start React Native: cd my-app && npm start', colors.yellow);
    log('3. Start Dashboard: cd dashbord-fastagcab && npm run dev', colors.yellow);
    log('\n🔑 Admin Login Credentials:', colors.cyan);
    log('Phone: 8959305283', colors.yellow);
    log('Password: 123456789', colors.yellow);

  } catch (error) {
    log(`\n❌ Setup failed: ${error.message}`, colors.red);
    process.exit(1);
  }
};

setupProject();

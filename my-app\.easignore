# EAS Build ignore file
# Exclude unnecessary files to reduce upload size and build time

# Documentation
*.md
docs/
README*

# Test files
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Development files
.expo/
.vscode/
.idea/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build artifacts
build/
dist/
android/build/
android/app/build/
ios/build/

# Temporary files
tmp/
temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
*.swp
*.swo
*~

# Error logs
android/hs_err_*.log
android/replay_*.log

# Examples and demos
examples/
demo/

# Large media files (if any)
*.mov
*.mp4
*.avi

# Development scripts
scripts/reset-project.js

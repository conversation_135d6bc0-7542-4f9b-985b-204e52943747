/**
 * Emergency fix for gift redemption points deduction
 * This script provides a direct replacement for the processRedemption function
 * that ensures both monthly and yearly points are deducted
 */

// Emergency processRedemption function
export const processRedemptionEmergencyFix = async (req, res) => {
  try {
    const { id } = req.params;
    const { action, adminNotes } = req.body;
    const adminId = req.user.id;

    console.log(`🚨 EMERGENCY FIX: Processing redemption ${id} with action: ${action}`);

    // Validate input
    if (!['approve', 'deny'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid action. Must be approve or deny'
      });
    }

    // Find redemption with populated user data
    const redemption = await GiftRedemption.findById(id).populate('userId');
    if (!redemption) {
      return res.status(404).json({
        success: false,
        message: 'Redemption request not found'
      });
    }

    // Check if already processed
    if (redemption.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Redemption request has already been processed'
      });
    }

    let updatedUser = null;
    let notificationMessage = '';
    let notificationType = 'info';

    if (action === 'approve') {
      console.log(`💰 EMERGENCY FIX: Deducting ${redemption.pointsRequired} points from user ${redemption.userId._id}`);
      
      // Get fresh user data
      const user = await User.findById(redemption.userId._id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      console.log(`👤 User before deduction - Monthly: ${user.monthlyPoints}, Yearly: ${user.yearlyPoints}`);

      // Check if user has sufficient points in BOTH balances
      if (user.monthlyPoints < redemption.pointsRequired) {
        return res.status(400).json({
          success: false,
          message: `Insufficient monthly points. Required: ${redemption.pointsRequired}, Available: ${user.monthlyPoints}`
        });
      }

      if (user.yearlyPoints < redemption.pointsRequired) {
        return res.status(400).json({
          success: false,
          message: `Insufficient yearly points. Required: ${redemption.pointsRequired}, Available: ${user.yearlyPoints}`
        });
      }

      // EMERGENCY FIX: Direct deduction from BOTH monthly and yearly points
      const originalMonthly = user.monthlyPoints;
      const originalYearly = user.yearlyPoints;

      user.monthlyPoints -= redemption.pointsRequired;
      user.yearlyPoints -= redemption.pointsRequired;

      // Ensure points don't go negative (safety check)
      user.monthlyPoints = Math.max(0, user.monthlyPoints);
      user.yearlyPoints = Math.max(0, user.yearlyPoints);

      console.log(`👤 User after deduction - Monthly: ${user.monthlyPoints}, Yearly: ${user.yearlyPoints}`);

      // Save user with updated points
      await user.save();
      updatedUser = user;

      // Approve the redemption
      redemption.status = 'approved';
      redemption.processedDate = new Date();
      redemption.processedBy = adminId;
      redemption.adminNotes = adminNotes || '';
      redemption.pointsDeducted = redemption.pointsRequired;
      await redemption.save();

      // Create point history entry
      try {
        await PointHistory.createEntry({
          userId: redemption.userId._id,
          transactionType: 'redeemed',
          pointsChange: -redemption.pointsRequired,
          pointsBalance: user.monthlyPoints,
          source: 'gift_redemption',
          sourceId: redemption._id,
          sourceModel: 'GiftRedemption',
          description: `EMERGENCY FIX: Redeemed ${redemption.pointsRequired} points for ${redemption.productName}`,
          metadata: {
            giftName: redemption.productName,
            adminId: adminId,
            adminNotes: adminNotes,
            emergencyFix: true,
            monthlyPointsBefore: originalMonthly,
            yearlyPointsBefore: originalYearly,
            monthlyPointsAfter: user.monthlyPoints,
            yearlyPointsAfter: user.yearlyPoints
          }
        });
        console.log('✅ Point history entry created');
      } catch (historyError) {
        console.error('⚠️ Point history creation failed:', historyError.message);
        // Don't fail the whole operation for history error
      }

      notificationMessage = `Congratulations! Your redemption request for ${redemption.productName} has been approved. ${redemption.pointsRequired} points have been deducted from both your monthly and yearly points.`;
      notificationType = 'success';

      console.log('✅ EMERGENCY FIX: Points deduction completed successfully');

    } else {
      // Deny redemption
      redemption.status = 'denied';
      redemption.processedDate = new Date();
      redemption.processedBy = adminId;
      redemption.adminNotes = adminNotes || '';
      await redemption.save();

      notificationMessage = `Your redemption request for ${redemption.productName} has been declined. ${adminNotes ? `Reason: ${adminNotes}` : ''}`;
      notificationType = 'error';
    }

    // Send notification to user
    try {
      const userNotification = new Notification({
        userId: redemption.userId._id,
        title: `Gift Redemption ${action === 'approve' ? 'Approved' : 'Declined'}`,
        message: notificationMessage,
        type: notificationType,
        data: {
          redemptionId: redemption._id,
          productName: redemption.productName,
          pointsRequired: redemption.pointsRequired,
          adminNotes,
          type: `gift_redemption_${action}d`
        },
        actionUrl: '/gifts'
      });
      await userNotification.save();
    } catch (notificationError) {
      console.error('⚠️ Notification creation failed:', notificationError.message);
      // Don't fail the whole operation for notification error
    }

    // Return success response
    res.json({
      success: true,
      message: `Redemption request ${action}d successfully`,
      data: {
        redemptionId: redemption._id,
        status: redemption.status,
        processedDate: redemption.processedDate,
        pointsDeducted: redemption.pointsDeducted,
        userPointsRemaining: {
          monthly: updatedUser ? updatedUser.monthlyPoints : redemption.userId.monthlyPoints,
          yearly: updatedUser ? updatedUser.yearlyPoints : redemption.userId.yearlyPoints
        },
        pointsDeductedFrom: 'both', // Always both in emergency fix
        emergencyFix: true
      }
    });

  } catch (error) {
    console.error('❌ EMERGENCY FIX: Process redemption error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to process redemption request',
      error: error.message
    });
  }
};

console.log(`
🚨 EMERGENCY FIX LOADED
======================

This emergency fix ensures that:
✅ Both monthly and yearly points are deducted
✅ Proper validation for sufficient points
✅ Point history is created
✅ Database is updated correctly

To use this fix:
1. Replace the processRedemption function in giftRedemptionController.js
2. Or import and use this function directly
3. Test with a small redemption first

The fix includes extensive logging for debugging.
`);

import cron from 'node-cron';
import PointsService from './pointsService.js';

class ScheduledTaskService {
  constructor() {
    this.tasks = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize all scheduled tasks
   */
  init() {
    if (this.isInitialized) {
      console.log('⚠️ Scheduled tasks already initialized');
      return;
    }

    console.log('🕐 Initializing scheduled tasks...');

    // Monthly points reset - runs at 12:01 AM on the 1st of every month
    this.scheduleMonthlyReset();

    // Yearly points reset - runs daily at 12:05 AM to check for anniversary resets
    this.scheduleYearlyReset();

    // Health check - runs every hour to ensure tasks are working
    this.scheduleHealthCheck();

    this.isInitialized = true;
    console.log('✅ All scheduled tasks initialized successfully');
  }

  /**
   * Schedule monthly points reset
   */
  scheduleMonthlyReset() {
    // Cron expression: minute hour day month day-of-week
    // '1 0 1 * *' = At 12:01 AM on the 1st day of every month
    const monthlyResetTask = cron.schedule('1 0 1 * *', async () => {
      console.log('🔄 Starting monthly points reset...');
      
      try {
        const startTime = new Date();
        const result = await PointsService.processMonthlyResetForAllUsers();
        const endTime = new Date();
        const duration = endTime - startTime;

        console.log('✅ Monthly points reset completed:', {
          duration: `${duration}ms`,
          ...result
        });

        // Log any errors
        if (result.errors.length > 0) {
          console.error('❌ Monthly reset errors:', result.errors);
        }

      } catch (error) {
        console.error('❌ Monthly points reset failed:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Kolkata' // Adjust timezone as needed
    });

    this.tasks.set('monthlyReset', monthlyResetTask);
    monthlyResetTask.start();
    console.log('📅 Monthly points reset scheduled (1st of every month at 12:01 AM)');
  }

  /**
   * Schedule yearly points reset check
   */
  scheduleYearlyReset() {
    // Cron expression: '5 0 * * *' = At 12:05 AM every day
    const yearlyResetTask = cron.schedule('5 0 * * *', async () => {
      console.log('🔄 Checking for yearly points resets...');
      
      try {
        const startTime = new Date();
        const result = await PointsService.processYearlyResetForAllUsers();
        const endTime = new Date();
        const duration = endTime - startTime;

        if (result.usersReset > 0) {
          console.log('✅ Yearly points reset completed:', {
            duration: `${duration}ms`,
            ...result
          });
        } else {
          console.log('ℹ️ No yearly resets needed today');
        }

        // Log any errors
        if (result.errors.length > 0) {
          console.error('❌ Yearly reset errors:', result.errors);
        }

      } catch (error) {
        console.error('❌ Yearly points reset check failed:', error);
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Kolkata' // Adjust timezone as needed
    });

    this.tasks.set('yearlyReset', yearlyResetTask);
    yearlyResetTask.start();
    console.log('📅 Yearly points reset check scheduled (daily at 12:05 AM)');
  }

  /**
   * Schedule health check for scheduled tasks
   */
  scheduleHealthCheck() {
    // Cron expression: '0 * * * *' = At the beginning of every hour
    const healthCheckTask = cron.schedule('0 * * * *', () => {
      const now = new Date();
      console.log(`💓 Scheduled tasks health check - ${now.toISOString()}`);
      
      // Check if all tasks are running
      const taskStatus = {};
      this.tasks.forEach((task, name) => {
        taskStatus[name] = task.running ? 'running' : 'stopped';
      });
      
      console.log('📊 Task status:', taskStatus);
    }, {
      scheduled: false,
      timezone: 'Asia/Kolkata'
    });

    this.tasks.set('healthCheck', healthCheckTask);
    healthCheckTask.start();
    console.log('💓 Health check scheduled (every hour)');
  }

  /**
   * Stop all scheduled tasks
   */
  stopAll() {
    console.log('🛑 Stopping all scheduled tasks...');
    
    this.tasks.forEach((task, name) => {
      task.stop();
      console.log(`🛑 Stopped task: ${name}`);
    });

    this.isInitialized = false;
    console.log('✅ All scheduled tasks stopped');
  }

  /**
   * Start all scheduled tasks
   */
  startAll() {
    console.log('▶️ Starting all scheduled tasks...');
    
    this.tasks.forEach((task, name) => {
      task.start();
      console.log(`▶️ Started task: ${name}`);
    });

    console.log('✅ All scheduled tasks started');
  }

  /**
   * Get status of all tasks
   */
  getStatus() {
    const status = {};
    this.tasks.forEach((task, name) => {
      status[name] = {
        running: task.running,
        scheduled: task.scheduled
      };
    });
    
    return {
      initialized: this.isInitialized,
      tasks: status
    };
  }

  /**
   * Manually trigger monthly reset (for testing)
   */
  async triggerMonthlyReset() {
    console.log('🔧 Manually triggering monthly reset...');
    
    try {
      const result = await PointsService.processMonthlyResetForAllUsers();
      console.log('✅ Manual monthly reset completed:', result);
      return result;
    } catch (error) {
      console.error('❌ Manual monthly reset failed:', error);
      throw error;
    }
  }

  /**
   * Manually trigger yearly reset check (for testing)
   */
  async triggerYearlyReset() {
    console.log('🔧 Manually triggering yearly reset check...');
    
    try {
      const result = await PointsService.processYearlyResetForAllUsers();
      console.log('✅ Manual yearly reset check completed:', result);
      return result;
    } catch (error) {
      console.error('❌ Manual yearly reset check failed:', error);
      throw error;
    }
  }
}

// Create singleton instance
const scheduledTaskService = new ScheduledTaskService();

export default scheduledTaskService;

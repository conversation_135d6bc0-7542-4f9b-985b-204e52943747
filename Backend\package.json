{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "seed": "node scripts/seedContent.js", "migrate-points": "node scripts/migratePointsSystem.js", "test-points": "node test-points-system.js", "test-deduction": "node test-points-deduction.js", "debug-redemption": "node debug-gift-redemption.js", "simple-test": "node simple-deduction-test.js", "direct-test": "node direct-points-test.js", "manual-check": "node manual-points-check.js", "test-redemption-flow": "node test-gift-redemption-flow.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"async-retry": "^1.3.3", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "express-validator": "^7.2.1", "form-data": "^4.0.4", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.3", "multer": "^2.0.1", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "path-to-regexp": "^6.2.1", "sanitize-html": "^2.17.0", "socket.io": "^4.7.5", "socket.io-client": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}
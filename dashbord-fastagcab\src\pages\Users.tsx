import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import {
  Search,
  Eye,
  Edit,
  Trash2,
  Plus,
  Download,
  RefreshCw,
  AlertCircle,
  Filter,
  Users as UsersIcon,
  UserCheck,
  UserX,
  Clock,
  MapPin,
  Phone,
  Award,
  Calendar,
  MoreHorizontal
} from 'lucide-react'
import Card, { CardHeader, CardTitle, CardContent } from '../components/ui/Card'
import Button from '../components/ui/Button'
import LoadingSpinner from '../components/ui/LoadingSpinner'
import { Badge } from '../components/ui/badge'
import EditUserModal from '../components/modals/EditUserModal'
import { usersAPI } from '../lib/api'
import { clsx } from 'clsx'
import toast from 'react-hot-toast'

interface User {
  _id: string
  fullName: string
  phoneNumber: string
  dateOfBirth: string
  age: number
  pinCode: string
  state: string
  city: string
  address: string
  adharNumber?: string
  panCardNumber?: string
  dealerCode: string
  status: 'pending' | 'approved' | 'denied'
  role: string
  profilePhoto?: string
  adharCard?: string
  panCard?: string
  bankDetails?: string
  monthlyPoints: number
  yearlyPoints: number
  createdAt: string
}

const Users: React.FC = () => {
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterRole, setFilterRole] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalUsers, setTotalUsers] = useState(0)
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    denied: 0,
    electricians: 0,
    distributors: 0,
    admins: 0,
    totalPoints: 0
  })

  // Calculate statistics from users
  const calculateStats = (userList: User[]) => {
    const stats = {
      total: userList.length,
      pending: userList.filter(u => u.status === 'pending').length,
      approved: userList.filter(u => u.status === 'approved').length,
      denied: userList.filter(u => u.status === 'denied').length,
      electricians: userList.filter(u => u.role === 'Electrician').length,
      distributors: userList.filter(u => u.role === 'Distributor').length,
      admins: userList.filter(u => u.role === 'admin').length,
      totalPoints: userList.reduce((sum, u) => sum + (u.monthlyPoints || 0) + (u.yearlyPoints || 0), 0)
    }
    setStats(stats)
  }

  // Filter users based on search and filters
  const filterUsers = (userList: User[]) => {
    let filtered = userList

    // Filter by role
    if (filterRole) {
      filtered = filtered.filter(u => u.role === filterRole)
    }

    // Filter by status
    if (filterStatus) {
      filtered = filtered.filter(u => u.status === filterStatus)
    }

    // Filter by search term
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase()
      filtered = filtered.filter(u =>
        u.fullName?.toLowerCase().includes(term) ||
        u.phoneNumber?.includes(term) ||
        u.dealerCode?.toLowerCase().includes(term) ||
        u.city?.toLowerCase().includes(term) ||
        u.state?.toLowerCase().includes(term)
      )
    }

    setFilteredUsers(filtered)
  }

  useEffect(() => {
    fetchUsers()
  }, [currentPage])

  useEffect(() => {
    filterUsers(users)
  }, [users, searchTerm, filterRole, filterStatus])

  const fetchUsers = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await usersAPI.getAll(currentPage, 50, '') // Get more users for better stats

      if (response.success && response.data) {
        const userList = response.data.users
        setUsers(userList)
        calculateStats(userList)
        setTotalPages(response.data.pagination?.totalPages || 1)
        setTotalUsers(response.data.pagination?.totalUsers || 0)
      } else {
        setError(response.message || 'Failed to load users')
        toast.error(response.message || 'Failed to load users')
      }
    } catch (error: any) {
      console.error('Failed to fetch users:', error)
      const message = error.response?.data?.message || 'Failed to load users'
      setError(message)
      toast.error(message)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteUser = async (userId: string, userName: string) => {
    if (!confirm(`Are you sure you want to delete ${userName}?`)) {
      return
    }

    try {
      await usersAPI.delete(userId)
      toast.success('User deleted successfully')
      fetchUsers() // Refresh the list
    } catch (error) {
      console.error('Failed to delete user:', error)
      toast.error('Failed to delete user')
    }
  }

  const handleEditUser = (user: User) => {
    setSelectedUser(user)
    setEditModalOpen(true)
  }

  const handleCloseEditModal = () => {
    setEditModalOpen(false)
    setSelectedUser(null)
  }

  const handleUserUpdated = () => {
    fetchUsers() // Refresh the list
  }

  // Get status badge variant for new UI
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'approved': return 'default' // Green
      case 'denied': return 'destructive' // Red/Orange
      case 'pending': return 'secondary' // Yellow/Grey
      default: return 'outline'
    }
  }

  // Get role badge variant
  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin': return 'destructive' // Orange for admin
      case 'Electrician': return 'default' // Green for electrician
      case 'Distributor': return 'secondary' // Grey for distributor
      default: return 'outline'
    }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <UserCheck className="h-3 w-3" />
      case 'denied': return <UserX className="h-3 w-3" />
      case 'pending': return <Clock className="h-3 w-3" />
      default: return <AlertCircle className="h-3 w-3" />
    }
  }

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      pending: 'badge-warning',
      approved: 'badge-success',
      rejected: 'badge-danger',
    }
    
    return (
      <span className={clsx('badge', statusClasses[status as keyof typeof statusClasses] || 'badge-gray')}>
        {status}
      </span>
    )
  }

  const getRoleBadge = (role: string) => {
    const roleClasses = {
      Electrician: 'badge-gray',
      Distributor: 'badge-success',
      admin: 'badge-danger',
    }
    
    return (
      <span className={clsx('badge', roleClasses[role as keyof typeof roleClasses] || 'badge-gray')}>
        {role}
      </span>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-neutral-900">Users</h1>
            <p className="text-neutral-600 mt-1">Manage all registered users</p>
          </div>
        </div>
        <Card>
          <CardContent className="p-12">
            <div className="flex flex-col items-center justify-center space-y-4">
              <LoadingSpinner size="lg" />
              <p className="text-neutral-500">Loading users...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-neutral-900">Users</h1>
            <p className="text-neutral-600 mt-1">Manage all registered users</p>
          </div>
          <Button
            onClick={fetchUsers}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Retry
          </Button>
        </div>
        <Card>
          <CardContent className="p-12">
            <div className="flex flex-col items-center justify-center space-y-4">
              <AlertCircle className="h-12 w-12 text-secondary-500" />
              <div className="text-center">
                <p className="text-neutral-900 font-medium">Failed to load users</p>
                <p className="text-neutral-500 mt-1">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-neutral-900">Users</h1>
          <p className="text-neutral-600 mt-1">Manage all registered users ({stats.total} total)</p>
        </div>
        <div className="flex flex-wrap gap-3">
          <Button
            onClick={fetchUsers}
            variant="outline"
            size="sm"
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add User
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-primary-100">
                <UsersIcon className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Total Users</p>
                <p className="text-2xl font-bold text-neutral-900">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-accent-100">
                <Clock className="h-6 w-6 text-accent-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Pending Approval</p>
                <p className="text-2xl font-bold text-neutral-900">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-primary-100">
                <UserCheck className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Approved</p>
                <p className="text-2xl font-bold text-neutral-900">{stats.approved}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-primary-100">
                <Award className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Total Points</p>
                <p className="text-2xl font-bold text-neutral-900">{stats.totalPoints.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Role Distribution Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-primary-100">
                <UsersIcon className="h-6 w-6 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Electricians</p>
                <p className="text-2xl font-bold text-neutral-900">{stats.electricians}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-secondary-100">
                <UsersIcon className="h-6 w-6 text-secondary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Distributors</p>
                <p className="text-2xl font-bold text-neutral-900">{stats.distributors}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-medium transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-lg bg-secondary-100">
                <UsersIcon className="h-6 w-6 text-secondary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-neutral-600">Admins</p>
                <p className="text-2xl font-bold text-neutral-900">{stats.admins}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400" />
                <input
                  type="text"
                  placeholder="Search by name, phone, dealer code, or location..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
            </div>

            {/* Role Filter */}
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-neutral-500" />
              <select
                value={filterRole}
                onChange={(e) => setFilterRole(e.target.value)}
                className="px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">All Roles</option>
                <option value="Electrician">Electrician</option>
                <option value="Distributor">Distributor</option>
                <option value="admin">Admin</option>
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">All Status</option>
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="denied">Denied</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Users ({filteredUsers.length} of {stats.total})</span>
            {(searchTerm || filterRole || filterStatus) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSearchTerm('')
                  setFilterRole('')
                  setFilterStatus('')
                }}
                className="text-xs"
              >
                Clear Filters
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {filteredUsers.length === 0 ? (
            <div className="text-center py-12">
              <AlertCircle className="h-12 w-12 text-neutral-400 mx-auto mb-4" />
              <p className="text-neutral-900 font-medium">No users found</p>
              <p className="text-neutral-500 mt-1">
                {searchTerm || filterRole || filterStatus
                  ? 'Try adjusting your search or filter criteria'
                  : 'No users have been registered yet'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-neutral-200">
                <thead className="bg-neutral-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Role
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Points
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-neutral-200">
                  {filteredUsers.map((user) => (
                    <tr key={user._id} className="hover:bg-neutral-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 bg-primary-100 rounded-full flex items-center justify-center">
                              <span className="text-sm font-medium text-primary-600">
                                {user.fullName?.charAt(0) || 'U'}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-neutral-900">
                              {user.fullName}
                            </div>
                            <div className="text-sm text-neutral-500 flex items-center gap-1">
                              <Phone className="h-3 w-3" />
                              {user.phoneNumber}
                            </div>
                            {user.dealerCode && (
                              <div className="text-xs text-neutral-400">
                                Code: {user.dealerCode}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge variant={getRoleBadgeVariant(user.role)}>
                          {user.role}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Badge variant={getStatusBadgeVariant(user.status)} className="flex items-center gap-1 w-fit">
                          {getStatusIcon(user.status)}
                          {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-900">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-neutral-400" />
                          <div>
                            <div>{user.city}</div>
                            <div className="text-neutral-500">{user.state}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-900">
                        <div className="flex items-center gap-1">
                          <Award className="h-3 w-3 text-neutral-400" />
                          <div>
                            <div>Monthly: {user.monthlyPoints || 0}</div>
                            <div className="text-neutral-500">Yearly: {user.yearlyPoints || 0}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2">
                          <Link
                            to={`/users/${user._id}`}
                            className="p-1 text-primary-600 hover:text-primary-900 hover:bg-primary-50 rounded transition-colors"
                            title="View Details"
                          >
                            <Eye className="h-4 w-4" />
                          </Link>
                          <button
                            className="p-1 text-neutral-600 hover:text-neutral-900 hover:bg-neutral-50 rounded transition-colors"
                            onClick={() => handleEditUser(user)}
                            title="Edit User"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            className="p-1 text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50 rounded transition-colors"
                            onClick={() => handleDeleteUser(user._id, user.fullName)}
                            title="Delete User"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-neutral-700">
                Showing page {currentPage} of {totalPages} ({stats.total} total users)
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(currentPage - 1)}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(currentPage + 1)}
                >
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Edit User Modal */}
      <EditUserModal
        user={selectedUser}
        isOpen={editModalOpen}
        onClose={handleCloseEditModal}
        onUserUpdated={handleUserUpdated}
      />
    </div>
  )
}

export default Users
